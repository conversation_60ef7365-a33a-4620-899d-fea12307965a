var layer, form, current_week_table, current_week_table2, my_send_task_table;;

function initializeTable(elem, url){
  layui.use(function(){
    var tableInstance = layui.table;
    tableInstance.render({
      id: elem,
      height: '400px',
      elem: `#${elem}`,
      url: url, //数据接口
      title: '工作台',
      skin: 'line',
      page: false, //开启分页
      toolbar: false,
      cols: [[
      {field: 'id', title: '编号', width: 80, hide: true},
        {field: 'name', align: 'left', title: '名称', minWidth: 200, templet: function (d) {
          var tag = '';
          if (d.daily_status_tag.length > 0){
            var tag = `<span class="seven-tag ${d.daily_status_tag[1]}">${d.daily_status_tag[0]}</span>`
          }
          return `<span style='color: #2468f2;' target="_blank" class='click_detail' data-href='${d.url}'>${d.name}${tag}</span>`;
        }},
        {field: 'priority', align: 'left', title: '优先级', width: 85, templet: function (d) {
          return `<span class="seven-tag ${d.priority[1]}">${d.priority[0]}</span>`;
        }},
        {field: 'data_type_title', align: 'left', title: '类型', width: 80},
        {field: 'status', align: 'left', title: '状态', width: 75},
        {field: 'project_name', align: 'left', title: '项目名称', minWidth: 180, templet: function (d) {
          return `<a style='color: #2468f2;' target="_blank" lay-href="/admin/projects/${d.project_id}" lay-title="${d.project_name}的项目详情">${d.project_name}</a>`;
        }},
        {field: 'around_time', align: 'left', title: '时间范围', minWidth: 300},
        {fixed: 'right', align: 'left', title: '操作', minWidth: 170, templet: function (d)
          {
            var button = '';
            d.button.forEach(res => {
              if (res.child && res.child.length > 0){
                res.child.forEach(child_res => {
                  button += `<a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal ${d.tab}_detail_${child_res.id}" data-around_time="${d.around_time}" data-uuid="${d.id}" data-project_id="${d.project_id}" data-event="${child_res.id}">${res.title}${child_res.title}</a>`
                })
              }else{
                button += `<a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal ${d.tab}_${res.id}" data-around_time="${d.around_time}" data-uuid="${d.id}" data-project_id="${d.project_id}" data-event="${res.id}">${res.title}</a>`
              }
            });
            return button;
          }
        }
      ]]
    });

    if (elem === 'data_modules_index') {
      current_week_table = tableInstance;
    } else {
      current_week_table2 = tableInstance;
    }
  })
}

// 本周任务搜索
layui.form.on('submit(search_data_modules_index)', function(data){
  var field = data.field; // 获得表单字段
  // 执行搜索重载
  current_week_table.reload('data_modules_index', {
    where: field
  });
  return false; // 阻止默认 form 跳转
});

// 下周任务搜索
layui.form.on('submit(search_data_modules_index2)', function(data){
  var field = data.field; // 获得表单字段
  // 执行搜索重载
  current_week_table.reload('data_modules_index2', {
    where: field
  });
  return false; // 阻止默认 form 跳转
});

layui.use(function(){
  layui.table.render({
    id: 'project-table',
    height: '300px',
    elem: `#project-table`,
    url: '/admin/welcomes/projects', //数据接口
    title: '项目列表',
    skin: 'line',
    page: false, //开启分页
    toolbar: false,
    cols: [[
      {field: 'project_name', align: 'left', title: '项目名称', minWidth: 180, templet: function (d) {
        return `<a style='color: #2468f2;' target="_blank" lay-href="/admin/projects/${d.id}" lay-title="${d.name}的项目详情">${d.name}</a>`;
      }},
      {field: 'project_type', align: 'left', title: '项目类型', width: 100},
      {field: 'organization_name', align: 'left', title: '所属组织', width: 150}
    ]]
  });
})


$(document).off('click', '#current_week, #next_week').on('click', '#current_week, #next_week', function(){
  layui.table.resize()
})

$(document).off('click', '.click_detail').on('click', '.click_detail', function(){
  $.ajax({
    type: 'GET',
    dataType: 'script',
    url: $(this).attr('data-href')
  })
})

// 项目任务
$(document).off('click', '.plans_in_progress, .plans_discontinued, .plans_completed, .plans_recovery').on('click', '.plans_in_progress, .plans_discontinued, .plans_completed, .plans_recovery', function(){
  var status = $(this).attr('data-event');
  var id = $(this).attr('data-uuid');
  var project_id = $(this).attr('data-project_id');

  if(status === 'in_progress' || status === 'completed'){
    layer.confirm("确认开始此任务吗？", function(index){
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_plan_status',
        data: {
          id: id,
          status: status
        }
        // ,success:function(res){
        //   if (res.status){
        //     layer.msg(res.message);
        //     update_count();
        //   }else{
        //     layer.msg(res.message);
        //   }
        // },error: function() {
        //   layer.msg('报错了，请联系管理员');
        // }
      })
      layer.close(index);
    })
  }else if(status === 'discontinued'){ // 中止
    layer.prompt({title: '请输入中止原因'}, function(value, index, elem){
      if(value === '') return elem.focus();
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_plan_status',
        data: {
          id: id,
          status: status,
          reason: value
        }
      })
      // 关闭 prompt
      layer.close(index);
    });
  }else if (status == 'recovery'){ // 恢复
    layer.confirm("确认恢复此任务吗？", function(index){
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_plan_status',
        data: {
          id: id,
          status: status
        }
      })
      layer.close(index);
    })
  }

});


$(document).off('click', '.work_orders_confirm_closed, .work_orders_manual_closed, .work_orders_appoint, .work_orders_unacceptance, .work_orders_solved, .work_orders_edit, .work_orders_deleted, .work_orders_add_receiver_user, .work_orders_unsolved, .work_orders_already_solved, .work_orders_open_again').on('click', '.work_orders_confirm_closed, .work_orders_manual_closed, .work_orders_appoint, .work_orders_unacceptance, .work_orders_solved, .work_orders_edit, .work_orders_deleted, .work_orders_add_receiver_user, .work_orders_unsolved, .work_orders_already_solved, .work_orders_open_again', function(){
  var status = $(this).attr('data-event');
  var id = $(this).attr('data-uuid');
  var project_id = $(this).attr('data-project_id');

  if (status == 'confirm_closed' || status == 'manual_closed'){
    layer.prompt({title: '请输入关闭原因', formType: 2}, function(value, index, elem){
      if(value === '') return elem.focus();
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_work_order_status',
        data: {
          id: id,
          status: status,
          reason: value
        }
        // ,
        // success: function(res){
        //   if (res.status){
        //     layer.msg(res.message);
        //     update_count();
        //   }else{
        //     layer.msg(res.message);
        //   }
        // }
      })
      layer.close(index);
    });
  }else if(status == 'appoint') { // 指派
    var obligation_user;
    layer.open({
      type: 1,
      title: '指派责任人',
      area: ['50%', '40%'],
      shade: 0.5,
      maxmin: false,
      offset: 'auto',
      content: `<div class="layui-form layui-padding-3">
                  <div style="width: 100%;" class="obligation_user_id"></div>
                </div>`,
      btn: ['保存', '关闭'],
      yes: function(index){
        $.ajax({
          type: 'POST',
          url: '/admin/welcomes/update_work_order_status',
          data: {
            id: id,
            status: status,
            obligation_user_id: obligation_user.getValue('value')[0]
          }
        })
        layer.close(index);
      },success: function(layero, index){
        $.ajax({
          url: "/admin/public_api/search_project_users",
          type: 'GET',
          data: {
            project_id: project_id
          },
          success:function(res){
            obligation_user = layui.xmSelect.render({
              el: '.obligation_user_id', // 将jQuery对象转换为DOM元素
              autoRow: true,
              radio: true,
              name: 'obligation_user_id',
              toolbar: { show: true },
              tips: '选择责任人',
              filterable: true,
              layVerify: 'required',
              remoteSearch: false,
              data: res.data
            });
          }
        })
      }
    });
  }else if(status == 'unacceptance'){ // 拒绝
    layer.prompt({title: '请输入驳回原因', formType: 2}, function(value, index, elem){
      if(value === '') return elem.focus();
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_work_order_status',
        data: {
          id: id,
          status: status,
          rejection_reason: value
        }
      })
      layer.close(index);
    });
  }else if(status == 'solved'){ // 解决
    layer.prompt({title: '请输入解决方案', formType: 2}, function(value, index, elem){
      if(value === '') return elem.focus();
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_work_order_status',
        data: {
          id: id,
          status: status,
          solution: value
        }
      })
      layer.close(index);
    });
  }else if(status == 'add_receiver_user'){ // 评估人
    layer.open({
      type: 1, // page 层类型
      area: ['50%', '50%'],
      title: '指定评估人',
      shadeClose: true, // 点击遮罩区域，关闭弹层
      maxmin: true, // 允许全屏最小化
      // 注: 这里特别对 select 设置了 lay-append-position 属性，以便与 layer 的定位方式保持一致
      content: `<div class="layui-form layui-padding-3">
                  <div style="width: 100%;" class="receiver_user_id"></div>
                </div>`,
      btn: ['保存', '关闭'],
      yes: function(index){
        $.ajax({
          type: 'POST',
          url: '/admin/welcomes/update_work_order_status',
          data: {
            id: id,
            status: status,
            receiver_user_id: receiver_user_id.getValue('value')[0]
          }
        })
        layer.close(index);
      },
      success: function () {
        $.ajax({
          url: "/admin/public_api/search_project_users",
          type: 'GET',
          data: {
            project_id: project_id
          },
          success:function(res){
            receiver_user_id = layui.xmSelect.render({
              el: '.receiver_user_id', // 将jQuery对象转换为DOM元素
              autoRow: true,
              radio: true,
              name: 'receiver_user_id',
              toolbar: { show: true },
              tips: '选择评估人',
              filterable: true,
              layVerify: 'required',
              remoteSearch: false,
              data: res.data
            });
          }
        })
      }
    });
  }else if (status == 'unsolved'){ // 未解决
    layer.prompt({title: '请输入未解决原因', formType: 2}, function(value, index, elem){
      if(value === '') return elem.focus();
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_work_order_status',
        data: {
          id: id,
          status: status,
          customer_confirmation: value
        }
      })
      layer.close(index);
    });
  }else if (status == 'already_solved'){ // 已解决
    layer.confirm("确认此任务已经解决，将其关闭吗？", function(index){
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_work_order_status',
        data: {
          id: id,
          status: status,
          reason: "任务已解决，发起人手动关闭！"
        }
      })
      layer.close(index);
    });
  }else if (status == 'open_again'){ // 重新打开
    $.ajax({
      type: 'GET',
      url: `/admin/work_orders/${id}/edit`,
      data: {
        event_tag: "open_again",
        project_id: project_id
      }
    })
  }

})

// 风险
$(document).off('click', '.project_risks_closed, .project_risks_appoint, .project_risks_converted').on('click', '.project_risks_closed, .project_risks_appoint, .project_risks_converted', function(){
  var status = $(this).attr('data-event');
  var id = $(this).attr('data-uuid');
  var project_id = $(this).attr('data-project_id');
  if (status == 'closed'){ // 关闭
    layer.prompt({title: '请输入关闭原因', formType: 2}, function(value, index, elem){
      if(value === '') return elem.focus();
      $.ajax({
        type: 'POST',
        url: '/admin/welcomes/update_project_risk_status',
        data: {
          id: id,
          status: status,
          reason: value
        }
      })
      layer.close(index);
    });
  }else if(status == 'appoint') { // 指派
    var obligation_user;
    layer.open({
      type: 1,
      title: '指派责任人',
      area: ['50%', '40%'],
      shade: 0.5,
      maxmin: false,
      offset: 'auto',
      content: `<div class="layui-form layui-padding-3">
                  <div style="width: 100%;" class="obligation_user_id"></div>
                </div>`,
      btn: ['保存', '关闭'],
      yes: function(index){
        $.ajax({
          type: 'POST',
          url: '/admin/welcomes/update_project_risk_status',
          data: {
            id: id,
            status: status,
            obligation_user_id: obligation_user.getValue('value')[0]
          }
        })
        layer.close(index);
      },success: function(layero, index){
        $.ajax({
          url: "/admin/public_api/search_project_users",
          type: 'GET',
          data: {
            project_id: project_id
          },
          success:function(res){
            obligation_user = layui.xmSelect.render({
              el: '.obligation_user_id', // 将jQuery对象转换为DOM元素
              autoRow: true,
              radio: true,
              name: 'obligation_user_id',
              toolbar: { show: true },
              tips: '选择责任人',
              filterable: true,
              layVerify: 'required',
              remoteSearch: false,
              data: res.data
            });
          }
        })

      }
    });
  }else if(status == 'converted') { // 转化
    var obligation_user;
    layer.open({
      type: 1,
      title: '请选择需要转化的类型',
      area: ['60%', '40%'],
      shade: 0.5,
      maxmin: false,
      offset: 'auto',
      content: `
        <div class="layui-form" style="height: 100%; display: flex;justify-content: center; align-items: center;">
          <style>
          /*
          * 基于复选框和单选框的卡片风格多选组件
          * 需要具备一些基础的 CSS 技能，以下样式均为外部自主实现。
          */
          /* 主体 */
          .layui-form-checkbox>.lay-skin-checkcard,
          .layui-form-radio>.lay-skin-checkcard {
            display: table;
            display: flex;
            padding: 12px;
            white-space: normal;
            border-radius: 10px;
            border: 1px solid #e5e5e5;
            color: #000;
            background-color: #fff;
          }
          .layui-form-checkbox>.lay-skin-checkcard>*,
          .layui-form-radio>.lay-skin-checkcard>* {
            /* display: table-cell; */  /* IE */
            vertical-align: top;
          }
          /* 悬停 */
          .layui-form-checkbox:hover>.lay-skin-checkcard,
          .layui-form-radio:hover>.lay-skin-checkcard {
            border-color: #2468f2;
          }
          /* 选中 */
          .layui-form-checked>.lay-skin-checkcard,
          .layui-form-radioed[lay-skin="none"]>.lay-skin-checkcard {
            color: #000;
            border-color: #2468f2;
            background-color: rgb(44 125 237 / 10%) !important;;
            /* box-shadow: 0 0 0 3px rgba(22, 183, 119, 0.08); */
          }
          /* 禁用 */
          .layui-checkbox-disabled>.lay-skin-checkcard,
          .layui-radio-disabled>.lay-skin-checkcard {
            box-shadow: none;
            border-color: #e5e5e5 !important;
            background-color: #eee !important;
          }
          /* card 布局 */
          .lay-skin-checkcard-avatar {
            padding-right: 8px;
          }
          .lay-skin-checkcard-detail {
            overflow: hidden;
            width: 100%;
          }
          .lay-skin-checkcard-header {
            font-weight: 500;
            font-size: 16px;
            white-space: nowrap;
            margin-bottom: 4px;
          }
          .lay-skin-checkcard-description {
            font-size: 13px;
            color: #5f5f5f;
          }
          .layui-disabled  .lay-skin-checkcard-description{
            color: #c2c2c2! important;
          }
          /* 选中 dot */
          .layui-form-checked>.lay-check-dot:after,
          .layui-form-radioed>.lay-check-dot:after {
            position: absolute;
            content: "";
            top: 2px;
            right: 2px;
            width: 0;
            height: 0;
            display: inline-block;
            vertical-align: middle;
            border-width: 10px;
            border-style: dashed;
            border-color: transparent;
            border-top-left-radius: 0px;
            border-top-right-radius: 6px;
            border-bottom-right-radius: 0px;
            border-bottom-left-radius: 6px;
            border-top-color: #2468f2;
            border-top-style: solid;
            border-right-color: #2468f2;
            border-right-style: solid;
            overflow: hidden;
          }
          .layui-checkbox-disabled>.lay-check-dot:after,
          .layui-radio-disabled>.lay-check-dot:after {
            border-top-color: #d2d2d2;
            border-right-color: #d2d2d2;
          }
          /* 选中 dot-2 */
          .layui-form-checked>.lay-check-dot-2:before,
          .layui-form-radioed>.lay-check-dot-2:before {
            position: absolute;
            font-family: "layui-icon";
            content: "";
            color: #fff;
            bottom: 4px;
            right: 3px;
            font-size: 9px;
            z-index: 12;
          }
          .layui-form-checked>.lay-check-dot-2:after,
          .layui-form-radioed>.lay-check-dot-2:after {
            position: absolute;
            content: "";
            bottom: 2px;
            right: 2px;
            width: 0;
            height: 0;
            display: inline-block;
            vertical-align: middle;
            border-width: 10px;
            border-style: dashed;
            border-color: transparent;
            border-top-left-radius: 6px;
            border-top-right-radius: 0px;
            border-bottom-right-radius: 6px;
            border-bottom-left-radius: 0px;
            border-right-color: #2468f2;
            border-right-style: solid;
            border-bottom-color: #2468f2;
            border-bottom-style: solid;
            overflow: hidden;
          }
          .layui-checkbox-disabled>.lay-check-dot-2:before,
          .layui-radio-disabled>.lay-check-dot-2:before {
            color: #eee !important;
          }
          .layui-checkbox-disabled>.lay-check-dot-2:after,
          .layui-radio-disabled>.lay-check-dot-2:after {
            border-bottom-color: #d2d2d2;
            border-right-color: #d2d2d2;
          }
          .lay-ellipsis-multi-line {
            overflow: hidden;
            word-break: break-all;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 3;
          }
        </style>

        <div class="layui-row layui-col-space20" style='padding-bottom: 30px;'>
          <div class="layui-col-xs12 layui-col-sm6 layui-col-md4">
            <input type="radio" name="exchange_task" value="plan" lay-skin="none">
            <div lay-radio class="lay-skin-checkcard lay-check-dot-2" style="height: 100px">
              <div class="lay-skin-checkcard-avatar">
                <svg style='width: 30px; height: 30px;' t="1752584881437" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="7030" width="144" height="144"><path d="M837.818182 1024H186.181818A186.181818 186.181818 0 0 1 0 837.818182V186.181818A186.181818 186.181818 0 0 1 186.181818 0h651.636364a186.181818 186.181818 0 0 1 186.181818 186.181818v651.636364a186.181818 186.181818 0 0 1-186.181818 186.181818zM735.697455 316.797673a55.854545 55.854545 0 0 0-55.854546-55.854546h-335.127273a55.854545 55.854545 0 0 0-55.854545 55.854546v474.763636l223.418182-130.327273 223.418182 130.327273v-474.763636zM452.328727 528.523636c2.141091-18.944 5.957818-46.545455 5.957818-46.545454s-23.645091-25.134545-36.864-40.429382a9.122909 9.122909 0 0 1 7.633455-14.894545c18.990545-3.435055 48.407273-8.9088 48.407273-8.9088s16.197818-28.215855 26.344727-45.512146c5.585455-10.5472 9.495273-9.309091 10.146909-9.020509a17.259055 17.259055 0 0 1 8.005818 9.020509c9.960727 17.296291 25.879273 45.521455 25.879273 45.521455s29.044364 5.464436 47.848727 8.899491a9.169455 9.169455 0 0 1 7.540364 14.894545c-13.125818 15.2576-36.584727 40.559709-36.584727 40.559709s3.723636 27.713164 6.050909 46.657164a9.541818 9.541818 0 0 1-14.708364 10.044509c-15.732364-7.503127-39.842909-19.195345-45.149091-21.867055-5.585455 2.541382-29.696 14.112582-45.614545 21.6064a9.672145 9.672145 0 0 1-14.894546-10.025891z" fill="#ffb800" p-id="7031" data-spm-anchor-id="a313x.search_index.0.i1.17cd3a81LjTD15" class="selected"></path></svg>
              </div>
              <div class="lay-skin-checkcard-detail">
                <div class="lay-skin-checkcard-header">项目计划</div>
                <div class="lay-skin-checkcard-description lay-ellipsis-multi-line">
                  将风险转化为项目计划
                </div>
              </div>
            </div>
          </div>

          <div class="layui-col-xs12 layui-col-sm6 layui-col-md4">
            <input type="radio" name="exchange_task" value="demand" lay-skin="none">
            <div lay-radio class="lay-skin-checkcard lay-check-dot-2" style="height: 100px">
              <div class="lay-skin-checkcard-avatar">
                <svg style='width: 30px; height: 30px;' t="1752584795464" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="5445" width="144" height="144"><path d="M849.170732 0c97.404878 0 174.829268 77.42439 174.829268 174.829268v674.341464c0 97.404878-77.42439 174.829268-174.829268 174.829268H174.829268c-97.404878 0-174.829268-77.42439-174.829268-174.829268V174.829268c0-97.404878 77.42439-174.829268 174.829268-174.829268h674.341464z m-247.258537 774.243902h-172.331707l67.434146 32.468293c9.990244 4.995122 22.478049 4.995122 34.965854 0l69.931707-32.468293z m7.492683-69.931707h-189.814634c-9.990244 0-19.980488 7.492683-19.980488 17.482927 0 9.990244 7.492683 17.482927 19.980488 17.482927h189.814634c9.990244 0 19.980488-7.492683 19.980488-17.482927 0-9.990244-7.492683-17.482927-19.980488-17.482927zM514.497561 237.268293c-62.439024 0-114.887805 19.980488-159.843902 62.439024-44.956098 42.458537-64.936585 92.409756-64.936586 152.35122v29.970731c0 7.492683 2.497561 14.985366 2.497561 19.980488v4.995122c2.497561 7.492683 4.995122 14.985366 7.492683 19.980488 2.497561 7.492683 4.995122 14.985366 9.990244 22.478049 7.492683 14.985366 17.482927 27.473171 27.473171 42.458536l17.482927 14.985366 2.497561 2.497561 4.995121 4.995122 2.497561 2.497561 9.990244 7.492683 2.497561 2.497561h2.497561c4.995122 4.995122 9.990244 9.990244 14.985366 17.482927 2.497561 7.492683 7.492683 12.487805 9.990244 14.985366 2.497561 2.497561 9.990244 4.995122 17.482927 4.995122H599.414634c7.492683 0 14.985366-2.497561 17.482927-4.995122 2.497561-2.497561 4.995122-7.492683 9.990244-14.985366 2.497561-7.492683 7.492683-12.487805 14.985366-17.482927h2.497561l2.497561-2.497561 9.990244-7.492683c0-2.497561 2.497561-2.497561 2.497561-2.497561 2.497561-2.497561 2.497561-4.995122 7.492682-7.492683h2.497561c4.995122-4.995122 12.487805-9.990244 17.482927-14.985366 4.995122-7.492683 9.990244-12.487805 14.985366-19.980488 4.995122-7.492683 9.990244-14.985366 12.487805-22.478048 2.497561-4.995122 4.995122-12.487805 9.990244-22.478049 2.497561-4.995122 4.995122-12.487805 7.492683-19.980488v-2.497561l2.497561-19.980488v-4.995122-24.975609c0-59.941463-22.478049-109.892683-67.434147-152.35122-37.463415-44.956098-89.912195-64.936585-152.351219-64.936585z m19.980488 69.931707c34.965854 0 64.936585 12.487805 92.409756 34.965854 27.473171 22.478049 39.960976 54.946341 39.960975 92.409756 0 9.990244-7.492683 17.482927-19.980487 17.482927s-19.980488-9.990244-19.980488-17.482927c0-24.97561-9.990244-47.453659-29.970732-64.936586-19.980488-17.482927-42.458537-27.473171-64.936585-27.47317-9.990244 0-19.980488-7.492683-19.980488-17.482927 2.497561-7.492683 12.487805-17.482927 22.478049-17.482927z" fill="#6966FD" p-id="5446"></path></svg>
              </div>
              <div class="lay-skin-checkcard-detail">
                <div class="lay-skin-checkcard-header">需求</div>
                <div class="lay-skin-checkcard-description lay-ellipsis-multi-line">
                  将风险转化为需求工单
                </div>
              </div>
            </div>
          </div>

          <div class="layui-col-xs12 layui-col-sm6 layui-col-md4">
            <input type="radio" name="exchange_task" value="bug" lay-skin="none">
            <div lay-radio class="lay-skin-checkcard lay-check-dot-2" style="height: 100px">
              <div class="lay-skin-checkcard-avatar">
                <svg style='width: 30px; height: 30px;' t="1752584860966" class="icon" viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="16937" width="144" height="144"><path d="M128 0h768a128 128 0 0 1 128 128v768a128 128 0 0 1-128 128H128a128 128 0 0 1-128-128V128a128 128 0 0 1 128-128z m648.128 290.368l-23.744 23.232c17.792 23.168 23.744 52.16 23.744 86.976 17.92-23.232 41.6-40.576 59.52-57.984 11.84-11.584 11.84-34.816-5.952-52.224-11.904-11.584-35.712-11.584-53.568 0z m-576.768 0c-17.856 17.408-17.856 40.64 0 57.984l53.504 52.224c-5.952-28.992 5.952-57.984 23.808-87.04-5.952-11.52-11.904-17.344-17.856-23.168-17.856-17.408-41.6-17.408-59.456 0zM128 563.008c0 17.344 17.856 34.752 41.6 34.752h77.312V516.48H157.76c-17.92 5.76-29.76 23.232-29.76 46.4z m166.528 260.928c11.84-5.76 17.792-17.408 29.696-23.232-17.856-23.168-29.696-46.4-47.552-69.568l-35.712 34.816c-17.792 11.52-11.84 34.752 0 52.16 11.904 17.408 35.712 17.408 53.568 5.76z m184.32 5.76c5.952 0 5.952 0 0 0 5.952-23.168 5.952-359.552 0-359.552 0-23.168-11.904-46.4-35.712-63.808-35.648-23.168-77.312-46.336-112.96-69.568-5.952-5.76-11.904 0-11.904 0A94.912 94.912 0 0 0 288.64 406.4v174.016c5.952 23.168 0 52.16 5.952 75.392 11.84 52.16 41.6 98.56 83.2 133.376 35.712 28.992 53.568 34.816 101.12 40.576z m0-475.52a63.808 63.808 0 0 0 77.312 0c41.6-23.168 77.248-46.4 118.912-69.568 5.952 0 5.952-5.824 5.952-5.824C675.072 197.568 603.712 128 520.448 128h-17.856c-83.2 11.584-142.72 75.392-142.72 150.784l5.952 5.76c35.712 23.232 77.312 46.464 113.024 69.632z m89.152 475.52c59.52-5.76 89.216-34.752 130.816-86.976 23.808-34.816 41.6-75.392 41.6-115.968V394.752c0-28.992-11.84-52.16-29.696-69.568-5.952 0-5.952-5.76-11.904 0-35.648 23.168-71.296 40.576-107.008 63.808-29.696 17.344-41.6 40.576-41.6 75.392 0 17.408-5.952 353.728-5.952 365.312h23.744z m225.984-11.52c11.904-11.648 17.856-40.64 5.952-52.224l-35.712-34.816c-17.792 23.168-29.696 46.4-47.552 69.568 5.952 5.76 11.904 17.408 23.808 23.232 17.856 11.52 41.6 11.52 53.504-5.76z m59.52-220.416c23.68 0 47.488-17.408 41.6-40.64 0-23.168-17.92-40.576-41.6-40.576h-77.376V597.76h77.312z" fill="#ff5722" p-id="16938"></path></svg>
              </div>
              <div class="lay-skin-checkcard-detail">
                <div class="lay-skin-checkcard-header">缺陷</div>
                <div class="lay-skin-checkcard-description lay-ellipsis-multi-line">
                  将风险转化为缺陷工单
                </div>
              </div>
            </div>
          </div>


        </div>
      </div>

      <script>
        layui.use(function () {
          var form = layui.form;
          var $ = layui.$;
          form.render();
        });
      </script>`,
      btn: ["确认转化", "关闭"],
      yes: function(index){
        var value = $('input[name="exchange_task"]:checked').val();
        if (value == 'demand'){
          $.ajax({
            type: 'GET',
            url: '/admin/work_orders/new',
            data: {
              project_id: project_id,
              tag: 'work_demand',
              project_risk_id: id
            }
          })
          layer.close(index);
        }else if (value == 'bug'){
          $.ajax({
            type: 'GET',
            url: '/admin/work_orders/new',
            data: {
              project_id: project_id,
              tag: 'work_bug',
              project_risk_id: id
            }
          })
          layer.close(index);
        }else if (value == 'plan'){
          $.ajax({
            type: 'GET',
            url: '/admin/plans/new',
            data: {
              project_id: project_id,
              project_risk_id: id
            }
          })
          layer.close(index);
        }else{
          layer.msg('请选择转化类型');
        }
      }
    });
  }

})

function getMySendFile() {
  var tab = $('.main-tabs .layui-tabs-header li.layui-this').attr('data-tab');
  var status = $('.sub-tabs li.layui-this').attr('data-status');
  var page = (tab !== 'processing');
  var where = {
    tab: tab,
    status: status
  };
  return { page, where };
}

// 重新加载表格数据
function reloadMySendTable() {
  var send_hash = getMySendFile();
  var seach_my_send_name = $('#seach_my_send_name').val();
  if (seach_my_send_name.length > 0){
    layui.table.reload('my_send_task', {
      where: {
        page: send_hash.page,
        name_cont: seach_my_send_name,
        tab: send_hash.where.tab,
        status: send_hash.where.status
      }
    });
  }else{
    initTable(send_hash.page, send_hash.where);
  }

}

// 搜索我发起的任务
layui.form.on('submit(search_my-send-table)', function(data){
  var field = data.field; // 获得表单字段
  var send_hash = getMySendFile();
  // 执行搜索重载
  var mergedObj = Object.assign({}, field, send_hash.where);
  console.log(mergedObj);
  layui.table.reload('my_send_task', {
    where: mergedObj
  });
  return false; // 阻止默认 form 跳转
});

// 初始化表格
function initTable(page=false, where={}) {
  layui.use('table', function() {
    my_send_task_table = layui.table;
    my_send_task_table.render({
      id: 'my_send_task',
      height: '400px',
      elem: '#my-send-table',
      url: '/admin/welcomes/my_send',
      where: where,
      title: '工作台',
      skin: 'line',
      page: page, //开启分页
      toolbar: false,
      cols: [[
        {field: 'id', title: '编号', width: 80, hide: true},
        {field: 'name', align: 'left', title: '名称', minWidth: 200, templet: function (d) {
          var tag = '';
          if (d.daily_status_tag.length > 0){
            var tag = `<span class="seven-tag ${d.daily_status_tag[1]}">${d.daily_status_tag[0]}</span>`
          }
          return `<span style='color: #2468f2;' target="_blank" class='click_detail' data-href='${d.url}'>${d.name}${tag}</span>`;
        }},
        {field: 'priority', align: 'left', title: '优先级', width: 85, templet: function (d) {
          return `<span class="seven-tag ${d.priority[1]}">${d.priority[0]}</span>`;
        }},
        {field: 'data_type_title', align: 'left', title: '类型', width: 80},
        {field: 'status', align: 'left', title: '状态', width: 75},
        {field: 'project_name', align: 'left', title: '项目名称', minWidth: 180, templet: function (d) {
          return `<a style='color: #2468f2;' target="_blank" lay-href="/admin/projects/${d.project_id}" lay-title="${d.project_name}的项目详情">${d.project_name}</a>`;
        }},
        {field: 'around_time', align: 'left', title: '时间范围', minWidth: 300},
        {fixed: 'right', align: 'left', title: '操作', minWidth: 170, templet: function (d)
          {
            var button = '';
            d.button.forEach(res => {
              if (res.child && res.child.length > 0){
                res.child.forEach(child_res => {
                  button += `<a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal ${d.tab}_detail_${child_res.id}" data-around_time="${d.around_time}" data-uuid="${d.id}" data-project_id="${d.project_id}" data-event="${child_res.id}">${res.title}${child_res.title}</a>`
                })
              }else{
                button += `<a class="layui-btn layui-btn-xs layui-btn-normal jh-btn-normal ${d.tab}_${res.id}" data-around_time="${d.around_time}" data-uuid="${d.id}" data-project_id="${d.project_id}" data-event="${res.id}">${res.title}</a>`
              }
            });
            return button;
          }
        }
      ]]
    });
  });
}

function update_count(){
  if (my_send_task_table){
    reloadMySendTable();
  }

  if ($('#current_week').hasClass('layui-this')) {
    current_week_table.reload('data_modules_index');
  } else {
    current_week_table2.reload('data_modules_index2');
  }
}

$(document).off('click', '.seven-tabs-tab').on('click', '.seven-tabs-tab', function() {
  // 移除所有标签页的激活状态
  $('.seven-tabs-tab').removeClass('seven-tabs-tab-active');

  // 添加激活状态到当前点击的标签
  $(this).addClass('seven-tabs-tab-active');

  // 获取当前标签索引
  const tabIndex = $(this).index();

  // 隐藏所有内容面板
  $('.seven-tabs-pane').removeClass('seven-tabs-pane-active');

  // 显示对应的内容面板
  $('.seven-tabs-pane').eq(tabIndex).addClass('seven-tabs-pane-active');
});