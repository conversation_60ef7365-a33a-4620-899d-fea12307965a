class Admin::ProjectUsersController < Admin::ApplicationController
  before_action :set_project_user, only: [:delete_role]
  before_action :set_project

  before_action do
    authorize ProjectUser
  end

  # GET /admin/project_users
  def index
    @project_power_keys = @project.project_power_keys(current_user_id)
    if request.xhr?
      @project_users = @project.project_users.joins(:user).where(organization_id: params[:organization_id], users: {organization_id: params[:organization_id]}).select('project_users.user_id').distinct
      @result = @project_users.to_a.map do |project_user|
        user = project_user.user
        role_name = user.project_users.where(project_id: @project.id, organization_id: params[:organization_id]).joins(:project_role_config).pluck('project_role_configs.name, project_users.id')
        {
          id: project_user.id,
          user_id: user.id,
          user_name: user.name,
          role_name: role_name
        }
      end
      render json: {code: 0, msg: 'success', data: @result}
    end
  end

  # GET /admin/project_users/1
  def show
  end

  # GET /admin/project_users/new
  def new
    @organization_id = params[:organization_id] || current_organization_id
    @project_user = ProjectUser.new(project_id: @project.id, organization_id: @organization_id)
    # if current_organization.parent_id.present?
    #   @select_data = @project.project_organizations.map do |project_organization|
    #     {name: project_organization.organization.name, children: User.select_many_user(project_organization.organization_id)}
    #   end
    # else
      @select_data = User.select_many_user(@organization_id)
    # end
  end

  # GET /admin/project_users/1/edit
  def edit
    @user = User.find(params[:user_id])
    @organization_id = params[:organization_id] || current_organization_id
    @check_list = @project.project_users.where(user_id: @user.id, organization_id: @organization_id).pluck(:project_role_config_id)
  end

  # POST /admin/project_users
  def create
    begin
      @status = true
      time_now = Time.now

      project_id = params[:project_user][:project_id]
      organization_id = params[:project_user][:organization_id]
      raise '项目不存在' if project_id.blank?
      user_ids = params[:user_ids].split(',')
      raise '未选择成员' if user_ids.blank?
      project_role_config_ids = params[:project_role_config_id].keys rescue []
      raise '未选择角色' if project_role_config_ids.blank?
      result_array = []
      user_ids.each do |user_id|
        project_role_config_ids.each do |project_role_config_id|
          result_array << {user_id: user_id, project_id: project_id, project_role_config_id: project_role_config_id, organization_id: organization_id,
            created_at: time_now, updated_at: time_now} unless ProjectUser.exists?(organization_id: organization_id, user_id: user_id, project_id: project_id, project_role_config_id: project_role_config_id)
        end
      end
      ProjectUser.insert_all!(result_array) if result_array.present?
      project = Project.find(project_id)
      url = "/admin/projects/#{project_id}"
      User.where(id: result_array.pluck(:user_id)).each do |user|
        UserNotice.init_message!(user.organization_id, user.id, project, "项目【#{project.name}】参与通知", "项目【#{project.name}】您已被添加为该项目成员。", url, url)
      end
    rescue => exception
      @status = false
      @msg = "失败了, #{exception.message}"
    end
  end

  # PATCH/PUT /admin/project_users/1
  def update
  end

  # DELETE /admin/project_users/1
  def destroy
    @status = true
    begin
      ActiveRecord::Base.transaction do
        @project.project_users.where(user_id: params[:user_id], organization_id: params[:organization_id]).destroy_all
        user = User.find(params[:user_id])
        UserNotice.init_message!(user.organization_id, user.id, @project, "项目【#{@project.name}】您被移除通知", "项目【#{@project.name}】您已被移除出此项目。", nil, nil)
      end
    rescue => exception
      @status = false
    end
  end

  def delete_role
    begin
      @status = true
      raise '请至少保留一个角色权限' if @project.project_users.where(user_id: @project_user.user_id, organization_id: params[:organization_id]).where.not(id: @project_user.id).blank?
      @project_user.destroy!
    rescue => exception
      @status = false
      @msg = exception.message
    end

  end

  def update_user_role
    begin
      @status = true
      ActiveRecord::Base.transaction do
        project_role_config_ids = params[:project_role_config_id].keys rescue []
        # 当前是否有身份
        # role_status = @project.project_users.where(user_id: params[:user_id]).present?

        @project.project_users.where(user_id: params[:user_id], organization_id: params[:organization_id]).where.not(project_role_config_id: project_role_config_ids).destroy_all
        array = []
        time_now = Time.now
        project_role_config_ids.each do |project_role_config_id|
          array << {user_id: params[:user_id], project_id: @project.id, project_role_config_id: project_role_config_id, organization_id: params[:organization_id],
            created_at: time_now, updated_at: time_now} unless ProjectUser.exists?(organization_id: params[:organization_id], user_id: params[:user_id], project_id: @project.id, project_role_config_id: project_role_config_id)
        end
        ProjectUser.insert_all!(array) if array.present?
        raise '请至少保留一个角色权限' if @project.project_users.where(user_id: params[:user_id]).blank?
      end
    rescue => exception
      @status = false
      @msg = "失败了, #{exception.message}"
    end

  end


  private
    # Use callbacks to share common setup or constraints between actions.
    def set_project_user
      @project_user = ProjectUser.find_by(id: params[:id], project_id: params[:project_id], organization_id: params[:organization_id] || current_organization_id)
    end

    def set_project
      @project = Project.find_by(id: params[:project_id])
    end

    # Only allow a trusted parameter "white list" through.
    def project_user_params
      params.require(:project_user).permit(:organization_id, :project_id, :project_role_config_id, :user_id)
    end
end
