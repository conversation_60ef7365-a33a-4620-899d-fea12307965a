class Admin::ApplicationController < ActionController::Base
  include Pundit
  include Pundit::Authorization
  before_action :check_user
  before_action :check_tab_redirect, unless: :skip_tab_redirect?
  helper_method :current_user, :current_user_id, :current_organization_id, :current_organization
  after_action :verify_authorized
  before_action :set_paper_trail_whodunnit

  rescue_from Pundit::NotAuthorizedError, with: :user_not_authorized

  # 后台登陆用户
  def current_user
    if session[:user_id]
      @current_user ||= User.find_by(id: session[:user_id])
    end
  end

  def current_user_id
    current_user.id
  end

  def current_organization
    current_user.organization
  end

  def current_organization_id
    current_user.organization_id
  end

  # 验证用户是否登陆
  def check_user
    return if current_user.present?
    # 保存原始请求路径（排除登录页面自身）
    original_path = request.original_fullpath
    unless original_path.start_with?(admin_sessions_path)
      # 将原始URL作为redirect_uri传递给登录页
      original_path = skip_source_type_url(original_path)
      redirect_to admin_sessions_path(redirect_uri: original_path)
    else
      # 如果已经是登录页面，直接重定向
      redirect_to admin_sessions_path
    end
  end

  def check_tab_redirect
    return unless current_user.present? # 仅限已登录用户
    return unless admin_route?          # 仅限 admin 路由

    # 检查是否包含 source-type=feishu 参数
    if params["source-type"] == "feishu"
      # 设置 flash[:new_tab_url] 为不包含 source-type 的 URL
      flash[:new_tab_url] = skip_source_type_url(request.original_url)
      redirect_to admin_path
    end
  end

  def admin_route?
    request.path.start_with?('/admin') && !request.path.start_with?(admin_sessions_path)
  end

  def skip_tab_redirect?
    # 跳过登录页、静态页等
    controller_name == 'sessions'
  end

  def user_not_authorized
    render "admin/exceptions/forbidden"
  end

  def skip_source_type_url(url)
    uri = Addressable::URI.parse(url)
    # 获取并清理查询参数
    query_params = uri.query_values || {}
    query_params.delete("source-type") # 移除 source-type 参数

    # 重新生成 URL
    uri.query = Addressable::URI.form_encode(query_params)
    uri.to_s
  end
end
