class Admin::CommentsController < Admin::ApplicationController
  before_action do
    authorize Comment
  end

  def create
    begin
      ActiveRecord::Base.transaction do
        @comment = Comment.new(comment_params)
        @comment.user = current_user
        @comment.organization_id = current_organization_id
        @commentable_type = params[:comment][:commentable_type]
        @commentable_id = params[:comment][:commentable_id]

        @status = @comment.save!

        hash = @comment.task_name_url
        if params[:comment][:at_user_ids].present?
          User.where(id: params[:comment][:at_user_ids]).each do |user|
            UserNotice.init_message!(user.organization_id, user.id, @comment, "你收到了一条新评论@提及", "您在“#{hash[:name]}”的评论中被@提及", hash[:url], hash[:feishu_url])
          end
        end
        @comment_tree = @comment.commentable.comments_json
      end
    rescue => exception
      Rails.logger.error exception
      @status = false
    end
  end

  # def update
  #   @comment = Comment.find(params[:id])
  #   @status = @comment.update(comment_params)
  # end

  def destroy
    @comment = Comment.find(params[:id])
    @status = @comment.destroy
    @commentable_type = params[:commentable_type]
    @commentable_id = params[:commentable_id]
    @comment_tree = @comment.commentable.comments_json
  end

  private

  def comment_params
    params.require(:comment).permit(:content, :parent_id, :commentable_id, :commentable_type, :at_user_ids)
  end

end