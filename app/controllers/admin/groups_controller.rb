class Admin::GroupsController < Admin::ApplicationController
  before_action do
    authorize Group
  end

  before_action :set_group, only: [:show, :edit, :update, :destroy, :add_user, :remove_user, :permissions, :update_permissions]

  # GET /admin/groups
  def index
    @q = current_user.organization.groups.order(created_at: :desc).ransack(params[:q])
    @groups = @q.result
    if request.xhr?
      @result = @groups.page(params[:page]).per(params[:limit]).map do |group|
        {
          id: group.id,
          name: group.name,
          description: group.description,
          users_count: group.users.count,
          created_at: group.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }
      end
      render json: {code: 0, data: @result, count: @groups.count}
    end
  end

  # GET /admin/groups/1
  def show
    if request.xhr?
      # 获取组内用户列表
      @users = @group.users.page(params[:page]).per(params[:limit])
      @result = @users.map do |user|
        {
          id: user.id,
          name: user.name,
          phone: user.phone,
          avatar: user.avatar_url,
          created_at: user.created_at.strftime('%Y-%m-%d %H:%M:%S')
        }
      end
      render json: {code: 0, data: @result, count: @group.users.count}
    end
  end

  # GET /admin/groups/new
  def new
    @group = current_user.organization.groups.new
  end

  # GET /admin/groups/1/edit
  def edit
  end

  # POST /admin/groups
  def create
    @group = current_user.organization.groups.new(group_params)
    if @group.save
      # 添加用户到组
      add_users_to_group if params[:select].present?
      # 添加权限到组
      add_permissions_to_group if params[:group][:permission_action_ids].present?
      @status = true
    else
      @status = false
      @msg = "失败了, #{@group.errors.full_messages.join(',')}"
    end
  end

  # PATCH/PUT /admin/groups/1
  def update
    if @group.update(group_params)
      # 更新组内用户
      update_group_users if params[:select].present?
      # 更新组权限
      update_group_permissions if params[:group][:permission_action_ids].present?
      @status = true
    else
      @status = false
      @msg = "失败了, #{@group.errors.full_messages.join(',')}"
    end
  end

  # DELETE /admin/groups/1
  def destroy
    if @group.destroy
      @status = true
    else
      @status = false
      @msg = "失败了, #{@group.errors.full_messages.join(',')}"
    end
  end

  # POST /admin/groups/1/add_user
  def add_user
    user = current_user.organization.users.find(params[:user_id])
    group_user = @group.group_users.build(
      user: user,
      organization: current_user.organization
    )

    if group_user.save
      render json: {status: true, msg: "用户添加成功"}
    else
      render json: {status: false, msg: "添加失败: #{group_user.errors.full_messages.join(',')}"}
    end
  rescue ActiveRecord::RecordNotFound
    render json: {status: false, msg: "用户不存在"}
  end

  # DELETE /admin/groups/1/remove_user
  def remove_user
    group_user = @group.group_users.find_by(user_id: params[:user_id])
    if group_user&.destroy
      render json: {status: true, msg: "用户移除成功"}
    else
      render json: {status: false, msg: "移除失败"}
    end
  end

  def permissions
    authorize @group, :permissions?
  end

  def update_permissions
    authorize @group, :update_permissions?
    if params[:group][:permission_action_ids].present?
      @group.group_permissions.destroy_all
      add_permissions_to_group
      render json: {status: true, msg: "权限更新成功"}
    else
      @group.group_permissions.destroy_all
      render json: {status: true, msg: "权限已清空"}
    end
  rescue => e
    render json: {status: false, msg: "更新失败: #{e.message}"}
  end

  # GET /admin/groups/available_users
  def available_users
    group_id = params[:group_id]
    if group_id.present?
      # 编辑模式：排除已在组内的用户
      group = current_user.organization.groups.find(group_id)
      excluded_user_ids = group.users.pluck(:id)
      @users = current_user.organization.users.where.not(id: excluded_user_ids)
    else
      # 新建模式：显示所有用户
      @users = current_user.organization.users
    end

    @users = @users.ransack({name_or_phone_cont: params[:q]}).result.order(created_at: :desc)
    result = @users.map do |user|
      {
        name: user.name,
        value: user.id,
        phone: user.phone
      }
    end
    render json: {status: true, data: result}
  end

  private

  def set_group
    @group = current_user.organization.groups.find(params[:id])
  end

  def group_params
    params.require(:group).permit(:name, :description)
  end

  def add_users_to_group
    user_ids = params[:select].split(',').map(&:to_i)
    user_ids.each do |user_id|
      user = current_user.organization.users.find_by(id: user_id)
      next unless user

      @group.group_users.find_or_create_by(
        user: user,
        organization: current_user.organization
      )
    end
  end

  def update_group_users
    # 清除现有关联
    @group.group_users.destroy_all
    # 添加新的关联
    add_users_to_group
  end

  def add_permissions_to_group
    permission_action_ids = params[:group][:permission_action_ids].reject(&:blank?).map(&:to_i)
    permission_action_ids.each do |permission_action_id|
      permission_action = current_user.organization.permission_actions.find_by(id: permission_action_id)
      next unless permission_action
      p permission_action.id
      p current_user.organization.id
      @group.group_permissions.find_or_create_by(
        permission_action: permission_action,
        organization: current_user.organization
      )
    end
  end

  def update_group_permissions
    @group.group_permissions.destroy_all
    add_permissions_to_group
  end
end
