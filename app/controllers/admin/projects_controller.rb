class Admin::ProjectsController < Admin::ApplicationController
  before_action do
    authorize Project
  end

  before_action :set_project, only: [:show, :edit, :update, :destroy, :edit_joint, :edit_support, :update_status]
  before_action :set_new, only: [:new_joint, :new, :new_support]
  before_action :set_chip_organization, only: [:new_joint, :new, :new_support, :edit_joint, :edit_support, :edit]

  # GET /admin/projects
  def index
    if request.xhr?
      @projects = Project.left_joins(:project_users).where("projects.user_id = ? OR project_users.user_id = ?", current_user.id, current_user.id).distinct.ransack(params[:q]).result
      @result = @projects.includes(:project_organizations).page(params[:page]).per(params[:limit]).map do |project|
        {
          id: project.id,
          name: project.name,
          chip_platform: project.chip_config&.name,
          project_type: project.project_type_i18n,
          type: project.project_type,
          product_name: project.product_name,
          description: project.description,
          main_purpose: project.main_purpose,
          design_in_at: project.design_in_at,
          evt_at: project.evt_at.strftime('%F'),
          dvt_at: project.dvt_at.strftime('%F'),
          pvt_at: project.pvt_at.strftime('%F'),
          mp_at: project.mp_at.strftime('%F'),
          status: project.status_i18n,
          team_size: project.team_size,
          username: project.user&.name,
          organization_name: project.organization.name,
          editable: project.editable? # 是否显示编辑按钮
        }
      end
      render json: {code: 0, msg: 'success', data: @result, count: @projects.count}
    end
  end

  def admin
    if request.xhr?
      @projects = Project.includes(:project_organizations).order(created_at: :desc).ransack(params[:q]).result
      @result = @projects.page(params[:page]).per(params[:limit]).map do |project|
        {
          id: project.id,
          name: project.name,
          chip_platform: project.chip_config&.name,
          project_type: project.project_type_i18n,
          type: project.project_type,
          product_name: project.product_name,
          description: project.description,
          main_purpose: project.main_purpose,
          design_in_at: project.design_in_at,
          evt_at: project.evt_at.strftime('%F'),
          dvt_at: project.dvt_at.strftime('%F'),
          pvt_at: project.pvt_at.strftime('%F'),
          mp_at: project.mp_at.strftime('%F'),
          status: project.status_i18n,
          team_size: project.team_size,
          username: project.user&.name,
          organization_name: project.organization.name,
          editable: project.editable? # 是否显示编辑按钮
        }
      end
      render json: {code: 0, msg: 'success', data: @result, count: @projects.count}
    end
  end

  # GET /admin/projects/1
  def show
    @progress_rate, @delay_rate = @project.show_project_progress
    @approval_flow = @project.current_approval_flow rescue nil
  end

  # GET /admin/projects/new
  def new
    @project.project_type = 'p_internal'
    @project.chip_organization_id = current_organization_id
  end

  def new_joint
    @project.project_type = 'p_joint'
    @chip_os_software_array = []
    @chip_os_version_array = []
    @project.chip_organization_id = current_organization_id
  end

  def new_support
    @project.project_type = 'p_support'
    @chip_os_software_array = []
    @project.chip_organization_id = current_organization_id
  end

  def edit_joint
    @chip_config_array = ChipConfig.where(organization_id: @project.chip_organization_id).map{|chip_config| {name: chip_config.name, value: chip_config.id, selected: @project.chip_config_id == chip_config.id}}
    @chip_os_software_array = ChipOsSoftware.where(chip_config_id: @project.chip_config_id, organization_id: @project.chip_organization_id).map{|chip_os_software| {name: chip_os_software.name, value: chip_os_software.id, selected: @project.chip_os_software_id == chip_os_software.id}}
    @chip_os_version_array = ChipOsVersion.where(chip_os_software_id: @project.chip_os_software_id, organization_id: @project.chip_organization_id).map{|chip_os_version| {name: chip_os_version.version, value: chip_os_version.id, selected: @project.chip_os_version_id == chip_os_version.id}}
    @users = User.where(organization_id: current_organization_id).pluck(:name, :id)
    @product_category_array = ProductCategory.where(organization_id: current_organization_id).pluck(:name, :id)
  end

  def edit_support
    @chip_config_array = ChipConfig.where(organization_id: @project.chip_organization_id).map{|chip_config| {name: chip_config.name, value: chip_config.id, selected: @project.chip_config_id == chip_config.id}}
    @chip_os_software_array = ChipOsSoftware.where(chip_config_id: @project.chip_config_id, organization_id: @project.chip_organization_id).map{|chip_os_software| {name: chip_os_software.name, value: chip_os_software.id, selected: @project.chip_os_software_id == chip_os_software.id}}
    @users = User.where(organization_id: current_organization_id).pluck(:name, :id)
    @product_category_array = ProductCategory.where(organization_id: current_organization_id).pluck(:name, :id)
  end

  # GET /admin/projects/1/edit
  def edit
    @chip_config_array = ChipConfig.where(organization_id: @project.chip_organization_id).map{|chip_config| {name: chip_config.name, value: chip_config.id, selected: @project.chip_config_id == chip_config.id}}
    @product_category_array = ProductCategory.where(organization_id: current_organization_id).pluck(:name, :id)
    @users = User.where(organization_id: current_organization_id).pluck(:name, :id)

    # @chip_os_version_array = @project.chip_config.chip_os_versions.pluck(:name, :id) if @project.chip_config
  end

  # POST /admin/projects
  def create
    @status = true
    begin
      ActiveRecord::Base.transaction do
        @project = Project.new(project_params)
        @project_organization_flow = OrganizationFlow.find_by(flow_type: 'f_project', organization_id: current_organization_id)

        set_project_name_and_status
        @project.save!

        p_status = if @project.p_internal? && @project_organization_flow.blank?
          'opened'
        else
          'applying'
        end
        @main_project_organization = @project.project_organizations.create!(organization_id: current_organization_id, p_type: 'owner', status: p_status)

        handle_post_save_logic
      end
    rescue => exception
      Rails.logger.error("创建项目异常: #{exception}")
      @status = false
      @msg = "创建项目异常: #{exception}"
    end

  end

  # PATCH/PUT /admin/projects/1
  def update
    begin
      @status = true
      ActiveRecord::Base.transaction do
        @project.update!(project_params)
        return if params[:project][:commit_status].blank?
        @project_organization_flow = OrganizationFlow.find_by(flow_type: 'f_project', organization_id: current_organization_id)

        new_status = set_project_status(@project, params[:project][:commit_status])
        @project.update!(status: new_status)

        @main_project_organization = @project.project_organizations.find_or_create_by(organization_id: current_organization_id, p_type: 'owner')
        @main_project_organization.update!(status: 'applying')
        @msg = "保存成功"
        handle_post_save_logic
      end
    rescue => exception
      Rails.logger.error("创建项目异常: #{exception}")
      @status = false
      @msg = "创建项目异常: #{exception}"
    end

  end

  def update_status
    begin
      case params[:status]
      when 'pending', 'closed', 'progressing', 'abnormal_closed'
        @project.update!(status: params[:status])
      end
      flash[:success] = "操作成功"
    rescue => exception
      Rails.logger.error("更新项目状态异常: #{exception}")
      flash[:error] = "操作失败, #{exception.message}"
    end
  end

  # DELETE /admin/projects/1
  def destroy
    @status = false
    @status = @project.destroy if @project.name == params[:confirm_name]
  end

  private
    # 设置项目名称和状态
    def set_project_name_and_status
      if params[:project][:setname] == 'auto'
        @project.name = generate_project_name(@project)
      end
      @project.status = set_project_status(@project, params[:project][:commit_status])
    end

    # 处理保存后的逻辑
    def handle_post_save_logic
      if @project.s_store?
        @msg = '已暂存'
      else
        handle_external_project
      end
    end

    # 处理外部项目逻辑
    def handle_external_project
      # 如果是技术支持案 或 联合开发案
      if @project.p_support? || @project.p_joint?
        # 这里需要上级审批 和上级联合，上级必须配置审核流程
        raise '您的企业没有上级，无法创建“技术支持案” 或 “联合开发案”' if current_organization.parent_id.blank?
        project_organization = @project.project_organizations.create!(
          organization_id: current_organization.parent_id,
          p_type: 'member',
          status: 'applying'
        )
        ApprovalFlow.build_approval(project_organization, current_user)
        @msg = '已提交申请'
      else
        if @main_project_organization.status == 'applying'
          ApprovalFlow.build_approval(@main_project_organization, current_user)
          @msg = '已提交申请'
        else
          @msg = '内部项目，没有配置审核流程，项目自动通过审核'
        end
      end
    end

    def generate_project_name(project)
      case project.project_type
      when 'p_internal' # 内部方案
        "#{project.chip_config.name}-#{project.product_category.name}-#{project.product_name}"
      when 'p_support' # 技术支持案
        "#{project.chip_config.name}-#{current_user.organization.name}-#{project.product_category.name}-技术支持案"
      when 'p_joint' # 联合开发案
        "#{project.chip_config.name}-#{current_user.organization.name}-#{project.product_category.name}-#{project.product_name}-#{project.terminal_customer_name}"
      end
    end

    # 设置项目状态
    def set_project_status(project, commit_status)
      if commit_status == 's_store'
        's_store'
      elsif project.p_internal? && @project_organization_flow.blank?
        'progressing'
      else
        'wait_open'
      end
    end

    def set_new
      @project = Project.new(user_id: current_user_id)
      @chip_config_array = ChipConfig.where(organization_id: current_organization_id).map{|chip_config| {name: chip_config.name, value: chip_config.id}}
      @product_category_array = ProductCategory.where(organization_id: current_organization_id).pluck(:name, :id)
      @users = User.where(organization_id: current_organization_id).pluck(:name, :id)
      # @users = User.where(organization_id: current_organization_id).pluck(:name, :id)
    end
    # Use callbacks to share common setup or constraints between actions.
    def set_project
      @project = Project.find_by(id: params[:id])
    end

    def set_chip_organization
      @chip_organization_array = [[current_organization.name, current_organization.id]]
      @chip_organization_array << [current_organization.parent.name, current_organization.parent_id] if current_organization.parent_id.present?
    end

    # Only allow a trusted parameter "white list" through.
    def project_params
      params.require(:project).permit(:name, :chip_config_id, :chip_organization_id, :chip_os_software_id, :chip_os_version_id, :project_type,
        :product_name, :product_category_id, :product_manager_id, :technical_manager_id, :business_contact_id,
        :description, :main_purpose, :manpower, :design_in_at, :evt_at, :dvt_at, :pvt_at, :mp_at,
        :status, :fcst_per_month, :mp_plus_six_months, :specification_file, :user_id,
        :terminal_customer_name, :team_size, :customer_project_name, :target_market_region,
        :agreement_accepted, :username, :phone, :email, :acceptor_name, :opening_at, :opening_desc, :main_competitiveness)
    end
end
