class Admin::SystemConfigsController < Admin::ApplicationController

  before_action do
    authorize SystemConfig
  end

  before_action :set_system_config, only: [:edit, :update]

  # GET /admin/system_configs/1/edit
  def edit
  end

  # PATCH/PUT /admin/system_configs/1
  def update
    if @system_config.update(system_config_params)
      redirect_to edit_admin_system_config_path(id: 1), notice: '更新成功'
    else
      render :edit
    end
  end

  private
    # Use callbacks to share common setup or constraints between actions.
    def set_system_config
      @system_config = SystemConfig.current(current_organization.id)
    end

    # Only allow a trusted parameter "white list" through.
    def system_config_params
      params.require(:system_config).permit(:organization_id, :feishu_app_id, :feishu_app_secret, :ding_agent_id,
        :ding_app_key, :ding_app_secret, :qiye_app_id, :qiye_agent_id, :qiye_secret, :email_address, :password,
        :smtp_server, :port, :authentication, :enable_tls, :enable_ssl, :domain, :feishu_status, :qiye_status,
        :ding_status, :email_status)
    end
end
