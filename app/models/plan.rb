# == Schema Information
#
# Table name: plans
#
#  id                           :bigint           not null, primary key
#  act_ended_at(实际结束时间)   :datetime
#  act_started_at(实际开始时间) :datetime
#  content(计划内容)            :string
#  deleted_at(删除时间)         :datetime
#  ended_at(计划结束时间)       :datetime
#  name(计划名称)               :string
#  p_priority(优先级)           :integer
#  p_type(类型)                 :integer
#  started_at(计划开始时间)     :datetime
#  status(状态)                 :integer
#  created_at                   :datetime         not null
#  updated_at                   :datetime         not null
#  duty_user_id(责任人ID)       :integer
#  organization_id(组织ID)      :integer
#  parent_id(父计划ID)          :integer
#  project_id(项目ID)           :integer
#  user_id(用户ID)              :integer
#
# Indexes
#
#  index_plans_on_duty_user_id     (duty_user_id)
#  index_plans_on_organization_id  (organization_id)
#  index_plans_on_project_id       (project_id)
#  index_plans_on_user_id          (user_id)
#
class Plan < ApplicationRecord
  acts_as_paranoid
  include Commentable
  has_paper_trail ignore: [:order_number, :level, :updated_at]

  belongs_to :project
  belongs_to :user
  belongs_to :parent_plan, foreign_key: "parent_id", class_name: "Plan", optional: true
  belongs_to :duty_user, foreign_key: "duty_user_id", class_name: "User"
  belongs_to :organization

  has_many :child_plans, foreign_key: "parent_id", class_name: "Plan", dependent: :destroy
  has_many :user_notices, as: :noticeable, dependent: :destroy

  validates :name, :started_at, :ended_at, presence: true
  validates :name, length: { maximum: 255 }
  validate :check_auth, :validate_start_and_end_time

  attr_accessor :sort_ids

  # 状态: 未开始, 进行中, 中止, 已完成
  enum status: {no_start: 1, in_progress: 2, discontinued: 3, completed: 4}
  # 任务类型， 1: 计划组，2: 计划 , 3: 里程碑
  enum p_type: {p_group: 1, p_plan: 2, p_milestone: 3}
  # 优先级: 1: 普通 2: 紧急 3: 严重
  enum p_priority: {p_normal: 1, p_urgent: 2, p_serious: 3}

  after_save :update_level, :notice_user

  def self.ransackable_attributes(auth_object = nil)
    ["act_ended_at", "act_started_at", "content", "created_at", "deleted_at", "duty_user_id", "ended_at", "id", "level", "name", "order_number", "organization_id", "p_priority", "p_type", "parent_id", "project_id", "started_at", "status", "updated_at", "user_id"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["child_plans", "comments", "duty_user", "organization", "parent_plan", "project", "user", "user_notices", "versions"]
  end

  def child_plan_ids
    sql = "WITH RECURSIVE parent_plans AS (
      SELECT id, name, parent_id
      FROM plans
      WHERE id = #{self.id}
      UNION
      SELECT f.id, f.name, f.parent_id
      FROM plans f
      JOIN parent_plans sf ON f.parent_id = sf.id
      WHERE f.id <> #{self.id}
    )
    SELECT id, name, parent_id
    FROM parent_plans
    WHERE id <> #{self.id};"
    result = ActiveRecord::Base.connection.execute sql
    ids = result.collect{|x| x["id"]}
    ids
  end

  def priority_color
    case self.p_priority
    when 'p_normal'
      [self.p_priority_i18n, "seven-tag-green"]
    when 'p_urgent'
      [self.p_priority_i18n, "seven-tag-yellow"]
    when 'p_serious'
      [self.p_priority_i18n, "seven-tag-red"]
    end
  end

  def get_url
    return {url: "/admin/plans/#{self.id}", feishu_url: "/admin/plans/#{self.id}/details"}
  end

  def daily_status_tag
    return [] unless (self.no_start? || self.in_progress?)
    if self.ended_at < Time.now
      return ['已延期', "seven-tag-red"]
    elsif (self.ended_at - 1.days) < Time.now
      return ['即将延期', "seven-tag-yellow"]
    else
      []
    end
  end

  def get_button(current_user)
    button=[]
    if current_user.id == self.duty_user_id
      button << {title: '开始', id: 'in_progress'} if self.no_start?
      button += [{title: '中止', id: 'discontinued'}, {title: '完成', id: 'completed'}] if self.in_progress?
    end
    button << {title: '恢复', id: 'recovery'} if self.discontinued? && current_user.id == self.user_id

    button
  end

  def data_type_title
    return "计划" if self.p_plan?
    return "计划组" if self.p_group?
    return "里程碑" if self.p_milestone?
  end

  def set_hash(user_id = nil)
    hash = {
      id: self.id,
      order_number: self.order_number,
      name: self.name,
      started_at: self.started_at.strftime('%F %T'),
      ended_at: self.ended_at.strftime('%F %T'),
      status: self.status_i18n,
      p_type: self.p_type_i18n,
      p_priority: self.p_priority_i18n,
      parent_id: self.parent_id,
      is_parent: self.child_plans.count>0,
      project_id: self.project_id,
      user_name: self.user.name,
      duty_user_name: self.duty_user.name,
      button: self.power_button(user_id),
      daily_status_tag: self.daily_status_tag,
      children: []
    }
    hash = self.get_child_hash(user_id, hash)
    hash
  end

  def get_child_hash(user_id=nil, hash={})
    self.child_plans.order(:order_number).each do |plan|
      result = {
        id: plan.id,
        order_number: plan.order_number,
        name: plan.name,
        started_at: plan.started_at.strftime('%F %T'),
        ended_at: plan.ended_at.strftime('%F %T'),
        status: plan.status_i18n,
        p_type: plan.p_type_i18n,
        p_priority: plan.p_priority_i18n,
        parent_id: plan.parent_id,
        is_parent: plan.child_plans.count>0,
        project_id: plan.project_id,
        user_name: plan.user.name,
        duty_user_name: plan.duty_user.name,
        button: plan.power_button(user_id),
        daily_status_tag: plan.daily_status_tag,
        children: []
      }
      hash[:children] << result
      plan.get_child_hash(user_id, result) if plan.child_plans.count > 0
    end
    hash
  end

  # 权限操作按钮
  def power_button(user_id)
    powes = project.project_power_keys(user_id)
    button = []
    button << {color: 'layui-border-blue', title: '<i class="layui-icon layui-icon-addition"></i>', id: 'add'} if powes.include?('add_plan')
    if powes.include?('edit_plan')
      button << {color: 'layui-border-green', title: '<i class="layui-icon layui-icon-edit"></i>', id: 'edit'} if !self.p_milestone? || powes.include?('milestone_change')
    end
    button << {color: 'layui-border-orange', title: '<i class="layui-icon layui-icon-eye"></i>', id: 'detail'} if powes.include?('destroy_plan')
    button << {color: 'layui-border-red', title: '<i class="layui-icon layui-icon-delete"></i>', id: 'deleted'} if powes.include?('show_plan')
    button
  end

  # def self.nested_tree(root_plan_id, project_id)
  #   sql_query = <<-SQL.squish
  #     WITH RECURSIVE cte AS (
  #       SELECT plans.*, users.name AS user_name, duty_users.name AS duty_user_name
  #       FROM plans
  #       LEFT JOIN users ON plans.user_id = users.id
  #       LEFT JOIN users AS duty_users ON plans.duty_user_id = duty_users.id
  #       WHERE plans.id = #{root_plan_id} and plans.project_id = #{project_id}
  #       UNION ALL
  #       SELECT plans.*, users.name AS user_name, duty_users.name AS duty_user_name
  #       FROM plans
  #       LEFT JOIN users ON plans.user_id = users.id
  #       LEFT JOIN users AS duty_users ON plans.duty_user_id = duty_users.id
  #       INNER JOIN cte ON plans.parent_id = cte.id
  #     )
  #     SELECT * FROM cte ORDER BY id;
  #   SQL

  #   results = find_by_sql(sql_query)

  #   # 构建嵌套树
  #   tree = {}
  #   results.each do |row|
  #     node = row.attributes.symbolize_keys.slice(
  #       :id, :order_number, :name, :started_at, :ended_at, :status,
  #       :p_type, :p_priority, :parent_id, :project_id, :user_name, :duty_user_name
  #     )
  #     node[:children] = []

  #     if row.parent_id.nil?
  #       tree[row.id] = node
  #     else
  #       p tree
  #       p row
  #       tree[row.parent_id][:children] << node
  #     end
  #   end

  #   tree.values.first
  # end

  def step_html(i)
    return ['waiting', "#{i}"] if self.no_start?
    return ['in-progress', '<i class="layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop"></i>'] if self.in_progress?
    return ['finished', '<i class="layui-icon layui-icon-ok"></i>'] if self.completed?
    return ['discontinued', '<i class="layui-icon layui-icon-close"></i>'] if self.discontinued?
  end

  # 导出项目计划
  def self.export_excel(project)
    header = ['ID', '序号', '名称', '内容', '开始时间', '结束时间', '级别', '状态', '优先级', '类型', '责任人']
    output = ExcelUtil.generate do |wb|
      styles = ExcelUtil.get_sheet_style(wb)
      wb.add_worksheet(name: '项目计划') do |sheet|
        sheet.add_row header, style: styles[:header]
        #导入内容
        project.plans.includes(:duty_user).order(:order_number).each do |plan|
          row = [plan.id, plan.order_number, plan.name, plan.content, plan.started_at.strftime('%F %T'), plan.ended_at.strftime('%F %T'),
          plan.level, plan.status_i18n, plan.p_priority_i18n, plan.p_type_i18n, plan.duty_user&.name]

          sheet.add_row row, types: :string, style: styles[:content]
        end
      end
    end
    output
  end

  # 导入项目计划
  def self.import_excel(file, project, user)
    organization_id = user.organization_id
    xlsx = file =~ /\.xlsx$/ ? Roo::Excelx.new(file) : Roo::Excel.new(file)
    sheet = xlsx.sheet('项目计划')
    sum = sheet.parse(header_search: []).count
    # 遍历每一行
    error_count = 0
    before_plan_first = nil
    before_plan_second = nil
    before_plan_third = nil
    before_plan_four = nil
    before_plan_five = nil
    before_plan_six = nil
    before_plan_seven = nil
    header = ['行数', '错误信息']

    output = ExcelUtil.generate do |wb|
      styles = ExcelUtil.get_sheet_style(wb)
      wb.add_worksheet(name: "错误信息") do |sheet_error|
        sheet_error.add_row header, style: styles[:header]
        sheet.parse(header_search: []).each_with_index do |hash, i|
          # 初始化 数据
          plan_hash = {id: nil, order_number: nil, name: nil, content: nil, started_at: nil, ended_at: nil, level: nil, status: nil, p_priority: nil, p_type: nil, duty_user: nil}
          # 遍历一行的每一列
          hash.each do |k,v|
            heard_key = k.to_s
            #
            # 'ID', '序号', '名称', '内容', '开始时间', '结束时间', '级别', '状态', '优先级', '类型', '责任人'
            next unless heard_key =~ /ID/ || heard_key =~ /序号/ || heard_key =~ /名称/ || heard_key =~ /内容/ || heard_key =~ /开始时间/ || heard_key =~ /开始时间/ || heard_key =~ /结束时间/ || heard_key =~ /级别/ || heard_key =~ /状态/ || heard_key =~ /优先级/ || heard_key =~ /类型/ || heard_key =~ /责任人/

            plan_hash[:id]           = v.to_s.chomp("\t") if heard_key =~ /ID/
            plan_hash[:order_number]         = v.to_s.chomp("\t") if heard_key =~ /序号/
            plan_hash[:name]         = v.to_s.chomp("\t") if heard_key =~ /名称/
            plan_hash[:content]       = v.to_s.chomp("\t") if heard_key =~ /内容/
            plan_hash[:started_at]     = v.to_s.chomp("\t").to_time if heard_key =~ /开始时间/
            plan_hash[:ended_at]       = v.to_s.chomp("\t").to_time if heard_key =~ /结束时间/
            plan_hash[:level]        = v.to_s.chomp("\t").to_i if heard_key =~ /级别/
            plan_hash[:status]       = v.to_s.chomp("\t") if heard_key =~ /状态/
            plan_hash[:p_priority]       = v.to_s.chomp("\t") if heard_key =~ /优先级/
            plan_hash[:p_type]    = v.to_s.chomp("\t") if heard_key =~ /类型/
            plan_hash[:duty_user]    = v.to_s.chomp("\t") if heard_key =~ /责任人/
          end

          begin
            duty_user = User.find_by(name: plan_hash[:duty_user], organization_id: organization_id)

            if duty_user.present? && !project.users.pluck(:id).include?(duty_user.id)
              project_role_config = ProjectRoleConfig.find_by(name: '普通用户')
              raise '项目成员角色配置缺少 “普通用户”' if project_role_config.blank?
              ProjectUser.create!(user_id: duty_user.id, project_id: project.id, project_role_config_id: project_role_config.id, organization_id: organization_id)
            end
            #状态 enum status: {no_start: 1, in_progress: 2, discontinued: 3, completed: 4}
            statuses = Plan.statuses.keys.map { |k| [I18n.t("enums.plan.status.#{k}"), k.to_s] }.to_h
            status = statuses[plan_hash[:status].to_s] || 'no_start'

            # 任务类型， 1: 计划组，2: 计划 , 3: 里程碑
            # enum p_type: {p_group: 1, p_plan: 2, p_milestone: 3}
            p_types = Plan.p_types.keys.map { |k| [I18n.t("enums.plan.p_type.#{k}"), k.to_s] }.to_h
            p_type = p_types[plan_hash[:p_type].to_s]

            # 优先级: 1: 普通 2: 紧急 3: 严重
            # enum p_priority: {p_normal: 1, p_urgent: 2, p_serious: 3}
            p_priorities = Plan.p_priorities.keys.map { |k| [I18n.t("enums.plan.p_priority.#{k}"), k.to_s] }.to_h
            p_priority = p_priorities[plan_hash[:p_priority].to_s]

            raise '开始时间不能小于结束时间' if plan_hash[:started_at].to_time > plan_hash[:ended_at].to_time

            if plan_hash[:id].blank?
              merge_status_hash = {}
              if status == 'completed'
                merge_status_hash.merge!({act_started_at: plan_hash[:started_at], act_ended_at: plan_hash[:ended_at]})
              elsif status == 'in_progress'
                merge_status_hash.merge!({act_started_at: plan_hash[:started_at]})
              end
              plan = Plan.new({
                order_number: plan_hash[:order_number],
                level: plan_hash[:level],
                name: plan_hash[:name],
                content: plan_hash[:content],
                project_id: project.id,
                started_at: plan_hash[:started_at].to_time,
                ended_at: plan_hash[:ended_at].to_time,
                status: status,
                p_priority: p_priority,
                p_type: p_type,
                duty_user_id: duty_user.id,
                user_id: user.id,
                organization_id: organization_id
              }.merge(merge_status_hash))
            else
              plan = Plan.find_by(id: plan_hash[:id])
              raise '任务ID 未查询到记录' if plan.blank?
              plan.update!(started_at: plan_hash[:started_at], ended_at: plan_hash[:ended_at])
            end
            if plan.level == 2
              raise '一级任务不存在' if before_plan_first.blank?
              plan.parent_id = before_plan_first.id
            elsif plan.level == 3
              raise '二级任务不存在' if before_plan_second.blank?
              plan.parent_id = before_plan_second.id
            elsif plan.level == 4
              raise '三级任务不存在' if before_plan_third.blank?
              plan.parent_id = before_plan_third.id
            elsif plan.level == 5
              raise '四级任务不存在' if before_plan_four.blank?
              plan.parent_id = before_plan_four.id
            elsif plan.level == 6
              raise '五级任务不存在' if before_plan_five.blank?
              plan.parent_id = before_plan_five.id
            elsif plan.level == 5
              raise '六级任务不存在' if before_plan_six.blank?
              plan.parent_id = before_plan_six.id
            elsif plan.level == 5
              raise '七级任务不存在' if before_plan_seven.blank?
              plan.parent_id = before_plan_seven.id
            end

            plan.save! if plan_hash[:id].blank?
            if plan_hash[:level] == 1
              before_plan_first = plan
            elsif plan_hash[:level] == 2
              before_plan_second = plan
            elsif plan_hash[:level] == 3
              before_plan_third = plan
            elsif plan_hash[:level] == 4
              before_plan_four = plan
            elsif plan_hash[:level] == 5
              before_plan_five = plan
            elsif plan_hash[:level] == 6
              before_plan_six = plan
            elsif plan_hash[:level] == 7
              before_plan_seven = plan
            end

          rescue => exception
            p exception.message
            sheet_error.add_row [i+2, exception.message]
            error_count += 2
          end
        end
      end
    end

    [output, error_count]
  end

  def build_change_log
    change_logs = []
    versions = self.versions.reorder(created_at: :desc)

    versions.each do |version|
      change_at = version.created_at.strftime("%Y-%m-%d %H:%M")
      change_by = User.find_by(id: version.whodunnit)&.name || "未知用户"
      change_type = version.event

      if version.event == "create"
        change_logs << {
          change_at: change_at,
          change_by: change_by,
          change_type: change_type,
          changes: "新建"
        }
      elsif version.event == "update"
        changeset = version.changeset.presence || {}
        formatted_changes = format_changeset_for_plan(changeset)

        unless formatted_changes.empty?
          change_logs << {
            change_at: change_at,
            change_by: change_by,
            change_type: change_type,
            changes: formatted_changes
          }
        end
      elsif version.event == "destroy"
        change_logs << {
          change_at: change_at,
          change_by: change_by,
          change_type: change_type,
          changes: "已删除"
        }
      end
    end

    change_logs
  end

  def format_changeset_for_plan(changeset)
    changes = []

    # 定义需要格式化为时间的字段
    time_fields = [:started_at, :ended_at, :act_started_at, :act_ended_at]

    # 定义需要翻译的字段及其对应 i18n 前缀
    translatable_fields = {
      status: 'enums.plan.status',
      p_type: 'enums.plan.p_type',
      p_priority: 'enums.plan.p_priority'
    }

    changeset.each do |field, (old_value, new_value)|
      next if ["id", "created_at", "deleted_at"].include?(field.to_s)

      field_sym = field.to_sym

      # 格式化时间字段
      if time_fields.include?(field_sym)
        old_value = Time.parse(old_value).strftime("%F %T") if old_value.present?
        new_value = Time.parse(new_value).strftime("%F %T") if new_value.present?
      end

      if field == 'duty_user_id'
        old_value = User.find(old_value).name if old_value.present?
        new_value = User.find(new_value).name if new_value.present?
      end

      if field == 'parent_id'
        old_value = Plan.find(old_value).name if old_value.present?
        new_value = Plan.find(new_value).name if new_value.present?
      end

      # 国际化枚举字段
      if translatable_fields.key?(field_sym)
        old_value = old_value.presence && I18n.t("#{translatable_fields[field_sym]}.#{old_value}")
        new_value = new_value.presence && I18n.t("#{translatable_fields[field_sym]}.#{new_value}")
      end
      field = I18n.t("activerecord.attributes.plans.#{field}")
      changes << {
        field: field,
        from: old_value,
        to: new_value
      }
    end

    changes
  end

  private
  # 用于新版本更新level等级数据
  def update_level
    if self.parent_id.present?
      self.update_columns(level: self.parent_plan.level + 1)
    else
      self.update_columns(level: 1)
    end
  end

  def check_auth
    return false if self.parent_id.blank?
    return false if self.id.blank?

    errors.add(:parent_id, "父级别任务不存在") and return if self.parent_plan.blank?

    errors.add(:parent_id, "父级别任务不能是自己") and return if self.parent_id == self.id

    errors.add(:parent_id, '当前父级任务已经是你的子任务') and return if self.child_plan_ids.include?(self.id)
  end

  def validate_start_and_end_time
    if self.started_at.present? && self.ended_at.present?
      errors.add(:error, "开始时间不能大于结束时间") and return if self.started_at > self.ended_at
    end

    return if self.parent_id.blank?
    if self.started_at.present? && self.parent_plan.started_at.present?
      errors.add(:error, "开始时间不能小于父级任务开始时间") and return if self.started_at < self.parent_plan.started_at
    end

    if self.ended_at.present? && self.parent_plan.ended_at.present?
      errors.add(:error, "结束时间不能大于父级任务结束时间") and return if self.ended_at > self.parent_plan.ended_at
    end
  end

  def notice_user
    # 责任人变更提醒
    # 1. 通知某人已将某某人已被从XX任务的负责人中移除
    # 2. 通知某人已被设置为XX任务的负责人
    change_commend = self.previous_changes
    if change_commend.has_key?(:duty_user_id)
      origin_duty_user_id = change_commend[:duty_user_id][0]
      new_duty_user_id = change_commend[:duty_user_id][1]

      if origin_duty_user_id.present?
        title = "任务【#{self.name}】-任务负责人变更提醒"
        content = "项目【#{self.project.name}】, 任务【#{self.name}】你已被移除该任务的负责人"
        hash = self.get_url
        user = User.find(origin_duty_user_id)
        UserNotice.init_message!(user.organization_id, origin_duty_user_id, self, title, content, hash[:url], hash[:feishu_url])
      end

      if new_duty_user_id.present?
        title = "任务【#{self.name}】-任务负责人任命提醒"
        content = "项目【#{self.project.name}】, 您已被任命为 任务：【#{self.name}】的负责人, 请及时处理！"
        hash = self.get_url
        user = User.find(new_duty_user_id)
        UserNotice.init_message!(user.organization_id, new_duty_user_id, self, title, content, hash[:url], hash[:feishu_url])
      end
    end

    # 状态变动提醒任务创建人和项目创建人
    if change_commend.has_key?(:status)
      if change_commend[:status].last.in?(['in_progress', 'discontinued', 'completed'])
        title = "任务【#{self.name}】状态变动提醒"
        content = "项目【#{self.project.name}】- 任务【#{self.name}】已变更为【#{self.status_i18n}】，请查看详情。"
        hash = self.get_url
        user_array = [self.user_id, self.project.user_id]
        User.where(id: user_array).each do |user|
          UserNotice.init_message!(user.organization_id, user.id, self, title, content, hash[:url], hash[:feishu_url])
        end
      end
    end

  end
end
