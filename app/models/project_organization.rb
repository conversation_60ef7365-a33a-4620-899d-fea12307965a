# == Schema Information
#
# Table name: project_organizations
#
#  id                                 :bigint           not null, primary key
#  deleted_at(删除时间)               :datetime
#  operated_at                        :datetime
#  p_type(组织类型 1: 组织者 2: 成员) :integer
#  status                             :integer
#  created_at                         :datetime         not null
#  updated_at                         :datetime         not null
#  organization_id(组织ID)            :integer
#  project_id(项目ID)                 :integer
#
# Indexes
#
#  index_project_organizations_on_organization_id  (organization_id)
#  index_project_organizations_on_project_id       (project_id)
#
class ProjectOrganization < ApplicationRecord
  acts_as_paranoid

  belongs_to :project
  belongs_to :organization
  has_many :approval_flows, as: :flowable, dependent: :destroy

  enum p_type: {owner: 1, member: 2}
  # 申请中:1, 申请失败:2, 已开通:3
  enum status: {applying: 1, failed: 2, opened: 3}

  validates :p_type, presence: true
  validate :only_one_owner_per_project_organization

  # 满足立项条件
  def approved_status
    time_now = Time.now
    if self.current_approval_flow.approved?
      self.project.update!(status: 'progressing')
      self.update!(status: 'opened', operated_at: time_now)

      main_project_organization = self.project.main_project_organization
      main_project_organization.update!(status: 'opened', operated_at: time_now) if self.member? && main_project_organization.present? && main_project_organization.applying?
    elsif self.current_approval_flow.rejected?
      self.update!(status: 'failed', operated_at: time_now)
      self.project.update!(status: 'rejected')
    end
  end

  def current_approval_flow
    self.approval_flows.find_by(is_effect: true)
  end

  private

  def only_one_owner_per_project_organization
    if owner? && ProjectOrganization.where(project_id: project_id, organization_id: organization_id, p_type: 'owner').where.not(id: id).exists?
      errors.add(:p_type, "同一个组织，同一个项目只能有一个组织者")
    end
  end

end
