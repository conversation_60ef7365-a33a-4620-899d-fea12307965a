# == Schema Information
#
# Table name: group_permissions
#
#  id                               :bigint           not null, primary key
#  deleted_at(删除时间)             :datetime
#  created_at                       :datetime         not null
#  updated_at                       :datetime         not null
#  group_id(用户组ID)               :integer
#  organization_id(组织ID)          :integer
#  permission_action_id(权限动作ID) :integer
#
# Indexes
#
#  index_group_permissions_on_deleted_at            (deleted_at)
#  index_group_permissions_on_group_id              (group_id)
#  index_group_permissions_on_organization_id       (organization_id)
#  index_group_permissions_on_permission_action_id  (permission_action_id)
#
class GroupPermission < ApplicationRecord
  acts_as_paranoid

  belongs_to :group
  belongs_to :permission_action
  belongs_to :organization

  validates :permission_action_id, uniqueness: { scope: :group_id, message: '权限不能重复添加到同一个用户组' }
end
