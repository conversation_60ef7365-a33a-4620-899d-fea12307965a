# == Schema Information
#
# Table name: user_notices
#
#  id                      :bigint           not null, primary key
#  content(内容)           :text
#  deleted_at(删除时间)    :datetime
#  name(标题)              :string
#  noticeable_type         :string
#  status(阅读状态)        :boolean          default(FALSE)
#  url(跳转链接)           :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  noticeable_id(多态)     :bigint
#  organization_id(组织id) :integer
#  user_id(用户id)         :integer
#
# Indexes
#
#  index_user_notices_on_noticeable       (noticeable_type,noticeable_id)
#  index_user_notices_on_organization_id  (organization_id)
#  index_user_notices_on_user_id          (user_id)
#
class UserNotice < ApplicationRecord
  acts_as_paranoid

  belongs_to :noticeable, polymorphic: true
  belongs_to :user
  belongs_to :organization

  validates :name, presence: true

  # 初始化通知内容
  def self.init_message!(organization_id, current_user_id, noticeable, title, content, url = nil, feishu_url = nil)
    begin
      UserNotice.create!(organization_id: organization_id, user_id: current_user_id, noticeable: noticeable, name: title, content: content, url: url, status: false)

      config = SystemConfig.current(organization_id)
      # 发送飞书消息
      send_feishu_message(organization_id, title, content, current_user_id, feishu_url) if config.feishu_status

      # 发送邮件通知
      send_email(organization_id, title, content, current_user_id, feishu_url) if config.email_status

    rescue => exception
      Rails.logger.error("Failed to create UserNotice: #{exception.message}")
    end
  end

  def self.send_feishu_message(organization_id, title, content, user_id, feishu_url=nil)
    user = User.find_by(id: user_id)
    return unless user&.feishu_user_id.present?

    if feishu_url.present?
      feishu_url = "#{Settings.request_url}#{feishu_url}?source-type=feishu"
      content = "#{content} \n <a href='#{feishu_url}'>查看详情</a>"
    end
    FeishuSendMessageJob.perform_later([user.feishu_user_id], title, content, organization_id)
  end

  def self.send_email(organization_id, title, content, user_id, feishu_url=nil)
    user = User.find_by(id: user_id)
    return unless user&.email.present?
    if feishu_url.present?
      feishu_url = "#{Settings.request_url}#{feishu_url}?source-type=feishu"
      content = "尊敬的#{user.name} \n #{content} \n <a href='#{feishu_url}'>查看详情</a>"
    end
    EmailService.deliver_later(to: user.email, subject: title, body: content, organization_id: organization_id)
  end

  # 链接打开方式
  def url_open_way
    return '' if url.blank?
    case noticeable_type
    when 'Plan', 'WorkOrder', 'Comment', 'ProjectRisk'
      return 'remote'
    when 'Project', 'ProjectOrganization'
      return 'new_windows'
    else
      return ''
    end
  end
end
