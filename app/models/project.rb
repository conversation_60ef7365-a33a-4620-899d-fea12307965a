# == Schema Information
#
# Table name: projects
#
#  id                                   :bigint           not null, primary key
#  acceptor_name(业务受理人)            :string
#  agreement_accepted(是否同意协议)     :boolean
#  customer_project_name(客户项目简称)  :string
#  deleted_at(删除时间)                 :datetime
#  description(项目描述)                :text
#  design_in_at(立项时间)               :datetime
#  dvt_at(设计验证时间)                 :datetime
#  email(邮箱)                          :string
#  evt_at(工程测试时间)                 :datetime
#  fcst_per_month(FCST/月)              :decimal(14, 2)
#  main_competitiveness(产品主要功能)   :string
#  main_purpose(项目目标、主要目的)     :string
#  mp_at(批量生产时间)                  :datetime
#  mp_plus_six_months(MP+6个月)         :decimal(14, 2)
#  name(项目名称)                       :string
#  opening_at(开案时间)                 :datetime
#  opening_desc(开案说明)               :text
#  phone(联系电话)                      :string
#  product_name(产品名称)               :string
#  project_type(项目类型)               :integer
#  pvt_at(小批量过程验证时间)           :datetime
#  specification_file(规格附件)         :string
#  status(状态)                         :integer
#  target_market_region(目标市场区域)   :string
#  team_size(项目人数)                  :integer
#  terminal_customer_name(终端客户简称) :string
#  username(姓名)                       :string
#  created_at                           :datetime         not null
#  updated_at                           :datetime         not null
#  business_contact_id(业务负责人)      :integer
#  chip_config_id(芯片平台ID)           :integer
#  chip_organization_id(芯片归属组织)   :integer
#  chip_os_software_id(OS系统软件ID)    :integer
#  chip_os_version_id(OS系统软件版本ID) :integer
#  product_category_id(产品类型ID)      :integer
#  product_manager_id(产品经理)         :integer
#  technical_manager_id(技术经理)       :integer
#  user_id(用户ID、项目创建人)          :integer
#
# Indexes
#
#  index_projects_on_chip_config_id        (chip_config_id)
#  index_projects_on_chip_organization_id  (chip_organization_id)
#  index_projects_on_chip_os_software_id   (chip_os_software_id)
#  index_projects_on_chip_os_version_id    (chip_os_version_id)
#  index_projects_on_product_category_id   (product_category_id)
#  index_projects_on_user_id               (user_id)
#
class Project < ApplicationRecord
  acts_as_paranoid

  belongs_to :user
  belongs_to :chip_config, optional: true
  belongs_to :chip_os_software, optional: true
  belongs_to :chip_os_version, optional: true
  belongs_to :product_category, optional: true

  belongs_to :product_manager, class_name: 'User', foreign_key: :product_manager_id, optional: true # 项目经理
  belongs_to :technical_manager, class_name: 'User', foreign_key: :technical_manager_id, optional: true # 技术经理
  belongs_to :business_contact, class_name: 'User', foreign_key: :business_contact_id, optional: true # 业务负责人
  belongs_to :chip_organization, class_name: 'Organization', foreign_key: :chip_organization_id


  has_many :plans, dependent: :destroy
  has_many :work_orders, dependent: :destroy
  has_many :user_notices, as: :noticeable, dependent: :destroy
  has_many :project_risks, dependent: :destroy

  attr_accessor :commit_status

  has_many :project_users, dependent: :destroy
  has_many :project_organizations, dependent: :destroy

  validates :name, :project_type, :product_name, :description, :main_purpose, :evt_at, :dvt_at, :pvt_at, :mp_at, :status, :fcst_per_month, :specification_file, presence: true

  # 项目类型: 内部方案: 1, 技术支持案: 2, 联合开发案: 3
  enum project_type: {p_internal: 1, p_support: 2, p_joint: 3}

  # 暂存:1  等待开启项目:2,  挂起:3 进行中:4, 已关闭:5, 驳回: 6
  enum status: {s_store: 1, wait_open: 2, pending: 3, progressing: 4, closed: 5, rejected: 6, abnormal_closed: 7}

  def self.ransackable_attributes(auth_object = nil)
    ["acceptor_name", "agreement_accepted", "business_contact_id", "chip_config_id", "chip_organization_id", "chip_os_software_id", "chip_os_version_id", "company_name", "company_type", "created_at", "customer_project_name", "deleted_at", "description", "design_in_at", "dvt_at", "email", "evt_at", "fcst_per_month", "id", "main_competitiveness", "main_purpose", "mp_at", "mp_plus_six_months", "name", "opening_at", "opening_desc", "phone", "product_category_id", "product_manager_id", "product_name", "project_type", "pvt_at", "specification_file", "status", "target_market_region", "team_size", "technical_manager_id", "terminal_customer_name", "updated_at", "user_id", "username"]
  end

  def self.ransackable_associations(auth_object = nil)
    ["business_contact", "chip_config", "chip_organization", "chip_os_software", "chip_os_version", "plans", "product_category", "product_manager", "project_organizations", "project_risks", "project_users", "technical_manager", "user", "user_notices", "work_orders"]
  end

  # 发布计划
  def show_release_plans
    return [false, "暂存状态，无法发布"] if self.s_store?
    return [false, "项目已被驳回申请"] if self.rejected?
    return [true, "项目待开启"] if self.wait_open?
    return [false, "挂起"] if self.pending?
    return [false, "进行中"] if self.progressing?
    return [false, "结项"] if self.closed?
  end

  # 项目进度
  def show_project_progress
    if self.progressing? || self.closed?
      completed_count = self.plans.completed.count + self.work_orders.where(aasm_state: 'close').count + self.project_risks.where(aasm_state: ['converted', 'closed']).count
      total_count = (self.plans.count + self.work_orders.count + self.delay_risks.count).to_f
      total_count = [total_count, 1].max
      progressing_rate = (completed_count / total_count) * 100

      # 延期总数
      delay_rate = ((WorkOrder.where_delay_work_order(self.id).count + self.delay_plans.count + self.delay_risks.count) / total_count) * 100
      [progressing_rate.round(2), delay_rate.round(2)]
    else
      return [0, 0]
    end
  end

  # 任务进度
  def plan_progress
    if self.progressing? || self.closed?
      return (self.plans.completed.count / [self.plans.count.to_f, 1.0].max).round(2) * 100
    else
      return 0
    end
  end

  # 风险进度
  def risk_progress
    if self.progressing? || self.closed?
      return (self.project_risks.where(aasm_state: ['converted', 'closed']).count / [self.project_risks.count.to_f, 1.0].max).round(2) * 100
    else
      return 0
    end
  end


  # 工单进度
  def work_progress(work_type=nil)
    work_orders = self.work_orders
    work_orders = self.work_orders.where(work_type: work_type) if work_type.present?

    if self.progressing? || self.closed?
      return (work_orders.where(aasm_state: %w[solved close]).count / [self.work_orders.count.to_f, 1.0].max).round(2) * 100
    else
      return 0
    end
  end

  # 延期任务
  def delay_plans
    self.plans.where(status: ['no_start', 'in_progress']).where("ended_at < ?", Time.now)
  end

  # 延期任务
  def delay_risks
    self.project_risks.where(aasm_state: ['identified', 'tracked']).where("ended_at < ?", Time.now)
  end

  def organization
    self.project_organizations.find_by(p_type: 'owner').organization
  end

  def main_project_organization
    self.project_organizations.find_by(p_type: 'owner')
  end

  def member_project_organization
    self.project_organizations.find_by(p_type: 'member')
  end

  def member_organizations
    self.project_organizations.where(p_type: 'member').map {|project_organization| project_organization.organization.name }.join(",")
  end

  def current_approval_flow
    if self.p_support? || self.p_joint?
      self.member_project_organization.approval_flows.find_by(is_effect: true)
    else
      self.main_project_organization.approval_flows.find_by(is_effect: true)
    end
  end

  # 权限列表 有多个企业就使用主企业配置的权限
  def project_power_keys(user_id)
    # 技术支持案 联合开发案
    status1 = self.p_support? || self.p_joint?
    # 如果是内部方案只使用自己企业的权限
    # 如果是技术支持案 或 联合开发案 都是用主企业的权限
    organization_id = (status1 && self.organization.parent_id.present?) ? self.organization.parent_id : self.organization.id
    if status1
      self.project_users.where(user_id: user_id).joins(project_role_config: :project_permission_configs)
        .where(project_role_configs: {organization_id: organization_id}).pluck('project_permission_configs.key').uniq.compact
    else
      self.project_users.where(user_id: user_id, organization_id: organization_id).joins(project_role_config: :project_permission_configs)
        .where(project_role_configs: {organization_id: organization_id}).pluck('project_permission_configs.key').uniq.compact
    end
  end

  def users
    User.active.joins(:project_users).where(project_users: {project_id: self.id}).distinct
  end

  def current_users(organization_id)
    User.active.joins(:project_users).where(organization_id: organization_id, project_users: {project_id: self.id, organization_id: organization_id}).distinct
  end

  # 用于@当前项目的成员 适用于ckediot
  def mentioned_users
    users.map { |user|
      {
        id: "@#{user.name}",
        userId: user.id,
        name: user.name
      }
    }
  end

  # def select_users
  #   result = users.map do |user|
  #     {
  #       title: "#{user.organization.name}#{user.name}",
  #       value: user.id
  #     }
  #   end
  #   result
  # end

  # 项目是否满足编辑要求
  def editable?
    return false if self.wait_open?
    return true if self.s_store? || self.p_internal? || self.rejected?
  end

  def new_or_edit_status
    return true if !self.persisted?
    return true if self.persisted? && (self.s_store? || self.rejected?)
    false
  end

end
