# == Schema Information
#
# Table name: groups
#
#  id                      :bigint           not null, primary key
#  description(用户组描述) :text
#  name(用户组名称)        :string
#  created_at              :datetime         not null
#  updated_at              :datetime         not null
#  organization_id(组织ID) :integer
#
# Indexes
#
#  index_groups_on_organization_id  (organization_id)
#
class Group < ApplicationRecord
  belongs_to :organization
  has_many :group_users, dependent: :destroy
  has_many :users, through: :group_users

  # 用户组权限关联
  has_many :group_permissions, dependent: :destroy
  has_many :permission_actions, through: :group_permissions

  validates :name, presence: true, length: { maximum: 255 }
  validates :name, uniqueness: { scope: :organization_id, message: '在同一个组织中用户组名称不能重复' }
  validates :description, length: { maximum: 1000 }

  scope :by_organization, ->(organization_id) { where(organization_id: organization_id) }
end
