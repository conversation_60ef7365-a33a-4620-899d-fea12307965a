# == Schema Information
#
# Table name: approval_steps
#
#  id                                                                                                            :bigint           not null, primary key
#  deleted_at(删除时间)                                                                                          :datetime
#  name(步骤名称)                                                                                                :string
#  order_number(步骤顺序)                                                                                        :integer
#  review_type(审核类型: 1: 会签（需所有审批人同意)、2: 或签(一名审批人同意即可)、3: 依次审批（按顺序依次审批）) :integer
#  created_at                                                                                                    :datetime         not null
#  updated_at                                                                                                    :datetime         not null
#  approval_flow_id(审核步骤ID)                                                                                  :integer
#  organization_id(组织ID)                                                                                       :integer
#
# Indexes
#
#  index_approval_steps_on_approval_flow_id  (approval_flow_id)
#  index_approval_steps_on_organization_id   (organization_id)
#
class ApprovalStep < ApplicationRecord
  acts_as_paranoid

  belongs_to :approval_flow
  belongs_to :organization

  has_many :approval_step_users, dependent: :destroy
  # 1: 会签（需所有审批人同意)、2: 或签(一名审批人同意即可)、3: 依次审批（按顺序依次审批）)
  enum review_type: {counter_sign: 1, or_sign: 2, serial_sign: 3 }

  # 等待中 1, 进行中 2, 已完成 3, 驳回 4
  enum status: {waiting: 1, in_progress: 2, approved: 3, rejected: 4}

  after_save :change_status

  def step_html
    return ['waiting', order_number.to_s] if self.waiting?
    return ['in-progress', '<i class="layui-icon layui-icon-loading-1 layui-anim layui-anim-rotate layui-anim-loop"></i>'] if self.in_progress?
    return ['finished', '<i class="layui-icon layui-icon-ok"></i>'] if self.approved?
    return ['discontinued', '<i class="layui-icon layui-icon-close"></i>'] if self.rejected?
  end

  def begin_next
    next_approval_step = self.approval_flow.approval_steps.find_by(order_number: self.order_number + 1)
    next_approval_step.start_next_step and return if next_approval_step.present? && self.approved?

    self.approval_flow.update!(status: 'approved') and return if self.approved? && next_approval_step.nil?
    self.approval_flow.update!(status: 'rejected') and return if self.rejected?
  end

  def start_next_step
    # 如果没有审核步骤，直接通过审核
    self.approval_flow.update!(current_step_id: self.id)
    self.update!(status: 'in_progress')
    if self.counter_sign? || self.or_sign? # 会签 || 或签
      self.approval_step_users.update_all(status: 'in_progress')
    else
      self.approval_step_users.order(:order_number).first.update!(status: 'in_progress')
    end
  end

  private
  def change_status
    return unless self.previous_changes.has_key?(:status)
    change_commend = self.previous_changes

    if change_commend[:status].last == 'approved' || change_commend[:status].last == 'rejected'
      title = "审批流程【#{approval_flow.source_name}】-审批节点状态变动提醒"
      content = "你发起人审批流程(#{approval_flow.source_name} 申请) 已变更为【#{self.status_i18n}】，请查看详情。"
      hash = self.approval_flow.get_url
      UserNotice.init_message!(organization_id, approval_flow.user_id, approval_flow.flowable, title, content, hash[:url], hash[:feishu_url])
    end
  end

end
