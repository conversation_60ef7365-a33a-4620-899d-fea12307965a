class EmailService
  class << self
    def send_email(to:, subject:, body:, organization_id:)
      config = SystemConfig.current(organization_id)
      form = config.email_address
      setup_smtp(config).tap do |smtp|
        message = build_message(form, to, subject, body)
        smtp.send_message message, form, to
      end
    rescue Net::SMTPFatalError => e
      Rails.logger.error "发送邮件失败: #{e.message}"
      raise
    end

    # 异步发送邮件（使用 ActiveJob）
    def deliver_later(to:, subject:, body:, organization_id:)
      EmailDeliveryJob.perform_later(to, subject, body, organization_id)
    end

    private

    def setup_smtp(config)
      smtp = Net::SMTP.new(config.smtp_server, config.port)
      smtp.set_debug_output $stdout

      # 最简单的方式启用 SSL
      if config.enable_ssl
        smtp.enable_ssl
      elsif config.enable_tls
        smtp.enable_starttls
      end

      smtp.start(
        config.domain,
        config.email_address,
        config.password,
        config.authentication.to_sym
      )

      smtp
    end


    def build_message(from, to, subject, body)
      <<~MESSAGE
        From: #{from}
        To: #{to}
        Subject: #{subject}

        #{body}
      MESSAGE
    end
  end
end