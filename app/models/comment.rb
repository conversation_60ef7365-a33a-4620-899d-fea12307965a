# == Schema Information
#
# Table name: comments
#
#  id                             :bigint           not null, primary key
#  commentable_type(评论对象类型) :string
#  content(评论内容)              :text
#  deleted_at(删除时间)           :datetime
#  created_at                     :datetime         not null
#  updated_at                     :datetime         not null
#  commentable_id(评论对象ID)     :integer
#  organization_id(组织ID)        :integer
#  parent_id(父级ID)              :integer
#  user_id(用户ID)                :integer
#
# Indexes
#
#  index_comments_on_commentable_type_and_commentable_id  (commentable_type,commentable_id)
#  index_comments_on_organization_id                      (organization_id)
#  index_comments_on_user_id                              (user_id)
#
class Comment < ApplicationRecord
  acts_as_paranoid

  belongs_to :user
  belongs_to :commentable, polymorphic: true
  belongs_to :organization
  belongs_to :parent_comment, class_name: 'Comment', foreign_key: 'parent_id', optional: true
  has_many :child_comments, class_name: 'Comment', foreign_key: 'parent_id', dependent: :destroy

  validates :content, presence: true

  after_create :send_reply_notification, if: -> { parent_comment.present? }

  attr_accessor :at_user_ids

  def formatted_time
    return "刚刚" if created_at > 5.minutes.ago
    return "#{(Time.now - created_at).to_i / 60}分钟前" if created_at > 1.hour.ago
    return "1小时前" if created_at > 90.minutes.ago
    return "#{(Time.now - created_at).to_i / 3600}小时前" if created_at > 1.day.ago
    created_at.strftime("%Y-%m-%d %H:%M")
  end

  # 返回 reply_to_user（被回复的用户名）
  def reply_to_user
    parent_comment&.user&.name.presence
  end

  # 转换为树结构所需格式
  def to_tree_hash
    {
      id: id,
      user: { name: user&.name || "匿名" },
      content: content,
      created_at: formatted_time,
      reply_to_user: reply_to_user,
      replies: []
    }
  end

  # 构建评论树结构
  def self.build_comment_tree(comments)
    comment_map = comments.each_with_object({}) do |comment, hash|
      hash[comment.id] = comment.to_tree_hash
    end

    root_comments = []

    comments.each do |comment|
      if comment.parent_id.present? && comment_map.key?(comment.parent_id)
        parent_hash = comment_map[comment.parent_id]
        parent_hash[:replies] ||= []
        parent_hash[:replies] << comment_map[comment.id]
      else
        root_comments << comment_map[comment.id]
      end
    end

    root_comments
  end

  def task_name_url
    case commentable_type
    when 'Plan'
      return {name: "#{commentable.project.name} - #{commentable.name}", url: "/admin/plans/#{commentable_id}", feishu_url: "/admin/plans/#{commentable_id}/details"}
    when 'WorkOrder'
      type = commentable.demand? ? '需求工单' : 'Bug工单'
      return {name: "#{type} - #{commentable.project.name} - #{commentable.name}", url: "/admin/work_orders/#{commentable_id}", feishu_url: "/admin/work_orders/#{commentable_id}/details"}
    when 'ProjectRisk'
      return {name: "#{commentable.project.name} - #{commentable.name}", url: "/admin/project_risks/#{commentable_id}", feishu_url: "/admin/project_risks/#{commentable_id}/details"}
    else
      return commentable.try(:name)
    end
  end

  # 发送通知
  def send_reply_notification
    # 获取被回复的用户
    replied_user = parent_comment.user
    hash = self.task_name_url
    content = "你在“#{hash[:name]}”下的评论被 #{self.user.name} 回复了：#{Nokogiri::HTML(self.content.truncate(20)).text}..."
    UserNotice.init_message!(replied_user.organization_id, replied_user.id, self, "你收到了一条新评论回复", content, hash[:url], hash[:feishu_url])
  end

end
