# == Schema Information
#
# Table name: organizations
#
#  id                                                :bigint           not null, primary key
#  deleted_at(删除时间)                              :datetime
#  name(企业名称)                                    :string
#  org_type(企业类型: 1: 代理商 2: 方案商 3: 品牌商) :integer
#  created_at                                        :datetime         not null
#  updated_at                                        :datetime         not null
#  parent_id(父级企业ID)                             :integer
#
# Indexes
#
#  index_organizations_on_parent_id  (parent_id)
#
class Organization < ApplicationRecord
  has_many :children, class_name: "Organization", foreign_key: "parent_id", dependent: :destroy
  has_many :users, dependent: :destroy
  has_many :roles, dependent: :destroy
  has_many :permission_controllers, dependent: :destroy
  has_many :permission_actions, dependent: :destroy
  has_many :role_permissions, dependent: :destroy
  has_many :user_roles, dependent: :destroy
  has_many :project_organizations, dependent: :destroy
  has_many :projects, through: :project_organizations

  # 用户组关联
  has_many :groups, dependent: :destroy
  has_many :group_users, dependent: :destroy
  has_many :group_permissions, dependent: :destroy

  # 企业分配的模块关联
  has_many :organization_permission_controllers, dependent: :destroy
  has_many :assigned_permission_controllers, through: :organization_permission_controllers, source: :permission_controller

  belongs_to :parent, class_name: "Organization", foreign_key: "parent_id", optional: true

  enum org_type: {
    agent: 1,
    solution_provider: 2,
    brand: 3
  }

  validates :name, presence: true
  validates :org_type, presence: true

  def self.ransackable_attributes(auth_object = nil)
    ["created_at", "deleted_at", "id", "name", "org_type", "parent_id", "updated_at"]
  end

  def generate_admin_auth
    abilities = YAML.load_file("#{Rails.root}/config/admin_auth.yml")
    abilities.each_with_index do |ability, index|
      permission_controller = PermissionController.find_or_create_by!(word: ability.keys.first, organization_id: self.id)
      permission_controller.update!(name: ability.dig(ability.keys.first), order_number: index + 1)
      ability['children'].each_with_index do |child, action_index|
        permission_action = permission_controller.permission_actions.find_or_create_by!(word: child.keys.first, organization_id: self.id)
        permission_action.update!(name: child.dig(child.keys.first), order_number: action_index + 1)
      end
    end
  end

  def project_create_button
    array = [{
      title: '内部方案',
      type: 'p_internal'
    }]
    # if self.parent_id.present?
    #   array << {title: '技术支持案', type: 'p_support'} if OrganizationFlow.find_by(flow_type: 'f_project_support', organization_id: self.parent_id).present?
    #   array << {title: '联合开发案', type: 'p_joint'} if OrganizationFlow.find_by(flow_type: 'f_project_joint', organization_id: self.parent_id).present?
    # end
    array << {title: '技术支持案', type: 'p_support'}
    array << {title: '联合开发案', type: 'p_joint'}
    array
  end

  def available_permission_controllers
    return [] if parent.blank?
    parent.permission_controllers
  end

  def assign_permission_controller(permission_controller_id)
    permission_controller = available_permission_controllers.find_by(id: permission_controller_id)
    return false if permission_controller.blank?

    organization_permission_controllers.find_or_create_by(permission_controller: permission_controller)
    true
  rescue ActiveRecord::RecordInvalid
    false
  end

  def remove_permission_controller(permission_controller_id)
    organization_permission_controllers.where(permission_controller_id: permission_controller_id).destroy_all
  end

  def has_permission_controller?(permission_controller_id)
    assigned_permission_controllers.exists?(id: permission_controller_id)
  end
end
