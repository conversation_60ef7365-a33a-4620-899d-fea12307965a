class GroupPolicy < ApplicationPolicy
  def index?
    check_auth("groups", "index")
  end

  def create?
    check_auth("groups", "create")
  end

  def update?
    check_auth("groups", "update")
  end

  def destroy?
    check_auth("groups", "destroy")
  end

  def new?
    create?
  end

  def edit?
    update?
  end

  def show?
    check_auth("groups", "index")
  end

  def add_user?
    check_auth("groups", "update")
  end

  def remove_user?
    check_auth("groups", "update")
  end

  def available_users?
    check_auth("groups", "index")
  end

  def permissions?
    check_auth("groups", "update")
  end

  def update_permissions?
    check_auth("groups", "update")
  end
end
