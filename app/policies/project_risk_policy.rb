class ProjectRiskPolicy < ApplicationPolicy

  def index?
    user.is_authority('view_project_risks', record.id)
  end

  def show?
    true
  end

  def create?
    true
  end

  def new?
    user.is_authority('new_project_risks', record.id)
  end

  def update?
    true
  end

  def edit?
    update?
  end

  def destroy?
    true
  end

  def change_state?
    true
  end

  def details?
    show?
  end

  def import_excel?
    user.is_authority('import_project_risk', record.id)
  end

  def down_excel?
    user.is_authority('import_project_risk', record.id)
  end

end