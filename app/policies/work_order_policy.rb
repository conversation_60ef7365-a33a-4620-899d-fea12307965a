

class WorkOrderPolicy < ApplicationPolicy

  def index?
    user.is_authority('view_work_orders', record.id)
  end

  def show?
    true
  end

  def create?
    true
  end

  def new?
    user.is_authority('new_work_orders', record.id)
  end

  def update?
    true
  end

  def edit?
    update?
  end

  def destroy?
    true
  end

  def evaluate_work_orders?
    true
  end

  def passing_work_orders?
    true
  end

  def change_state?
    true
  end

  def project_demand_works?
    true
  end

  def project_bug_works?
    true
  end

  def import_excel?
    user.is_authority('import_work_order', record.id)
  end

  def down_excel?
    user.is_authority('import_work_order', record.id)
  end


  def details?
    show?
  end

end