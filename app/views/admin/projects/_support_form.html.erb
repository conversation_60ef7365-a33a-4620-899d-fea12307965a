<!-- 技术支持发案 -->
<%= simple_form_for([:admin, @project], remote: true, wrapper: :seven_form_line, html: {class: 'layui-form', id: "project_form"}) do |f| %>
  <%= f.error_notification %>
  <%= f.input :commit_status, label: '提交状态', wrapper_html: {style: 'display:none'} %>

  <div class="layui-form string optional">
    <div class="layui-form-item">
      <label class="layui-form-label string optional" for="">项目名称</label>
      <div class="layui-input-block flex-item">
        <input type="radio" name="project[setname]" lay-filter="setname-radio-filter" value="auto" title="自动生成" checked>
        <input type="radio" name="project[setname]" lay-filter="setname-radio-filter" value="customize" title="自定义项目名称" disabled>
      </div>
    </div>
  </div>

  <%= f.input :name, label: '自定义名称', wrapper_html: {'style': 'display: none'} %>

  <div class="project-form">
    <%= f.input :username, label: '姓名', input_html: {'lay-verify': "required", value: f.object&.username || current_user.name} %>
    <%= f.input :team_size, label: '项目人数', input_html: {'lay-verify': "required"} %>
  </div>

  <div class="project-form">
    <%= f.input :phone, label: '联系电话', input_html: {'lay-verify': "required", value: f.object&.phone || current_user.phone} %>
    <%= f.input :customer_project_name, label: '客户项目简称' %>
  </div>

  <div class="project-form">
    <%= f.input :email, label: '邮箱', input_html: {'lay-verify': "required|email", value: f.object&.email || current_user.email}  %>
    <%= f.input :business_contact_id, label: '业务受理人', collection: @users %>
  </div>

  <%= f.input :project_type, label: '类别', input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'}%>

  <%= f.input :chip_organization_id, label: '芯片来源', collection: @chip_organization_array, input_html: {'lay-verify': "required", "lay-filter": "chip_organization_id"} %>

  <div class="project-form">
    <div class="layui-form string optional">
      <div class="layui-form-item">
        <label class="layui-form-label string optional" for="">芯片平台</label>
        <div class="layui-input-block flex-item">
          <div id="select_chip_config_id" style="width: 99%;" data-value="<%= @chip_config_array.to_json %>"></div>
        </div>
      </div>
    </div>

    <div class="layui-form string optional">
      <div class="layui-form-item">
        <label class="layui-form-label string optional" for="">OS系统</label>
        <div class="layui-input-block flex-item">
          <div id="select_chip_os_software_id" style="width: 99%;" data-value="<%= @chip_os_software_array.to_json %>"></div>
        </div>
      </div>
    </div>
    <%#= f.input :chip_config_id, collection: @chip_config_array, label: '芯片平台', input_html: {'lay-verify': "required"} %>
    <%#= f.input :chip_os_software_id, collection: @chip_os_software_array, label: 'OS 系统版本', input_html: {'lay-verify': "required"} %>
  </div>

  <div class="project-form">
    <%= f.input :product_name, label: '产品名称', input_html: {'lay-verify': "required"} %>
    <%= f.input :product_category_id, collection: @product_category_array, label: '产品类型', input_html: {'lay-verify': "required"} %>
  </div>

  <div class="project-form">
    <%= f.input :terminal_customer_name, label: '终端客户简称', input_html: {'lay-verify': "required"} %>
    <%= f.input :target_market_region, label: '目标市场区域', input_html: {'lay-verify': "required"} %>
  </div>

  <div class="project-form">
    <%= f.input :fcst_per_month, label: 'FCST/月', input_html: {'lay-verify': "required"} %>
    <%= f.input :opening_at, label: '开案时间', input_html: {'lay-verify': "required", class: 'opening_at', autocomplete: 'off', value: f.object.opening_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择开案时间", readonly: true, unit: "&#xe637;"%>
  </div>

  <div class="project-form">
    <%= f.input :evt_at, label: 'EVT时间', input_html: {'lay-verify': "required", class: 'evt_at', autocomplete: 'off', value: f.object.evt_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择EVT时间", readonly: true, unit: "&#xe637;"%>
    <%= f.input :pvt_at, label: 'PVT时间', input_html: {'lay-verify': "required", class: 'pvt_at', autocomplete: 'off', value: f.object.pvt_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择PVT时间", readonly: true, unit: "&#xe637;"%>
  </div>

  <div class="project-form">
    <%= f.input :dvt_at, label: 'DVT时间', input_html: {'lay-verify': "required", class: 'dvt_at', autocomplete: 'off', value: f.object.dvt_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择DVT时间", readonly: true, unit: "&#xe637;"%>
    <%= f.input :mp_at, label: 'MP时间', input_html: {'lay-verify': "required", class: 'mp_at', autocomplete: 'off', value: f.object.mp_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择MP时间", readonly: true, unit: "&#xe637;"%>
  </div>



  <%= f.input :description, as: :text, label: '项目背景', input_html: {'lay-verify': "required", class: 'layui-textarea'} %>
  <%= f.input :main_competitiveness, as: :text, label: '产品主要功能', input_html: {'lay-verify': "required", class: 'layui-textarea'} %>
  <%= f.input :opening_desc, as: :text, label: '开案说明', input_html: {'lay-verify': "required", class: 'layui-textarea'} %>
  <%= f.input :main_purpose, label: '项目目标', input_html: {'lay-verify': "required"} %>
  <%#= f.input :design_in_at, label: '立项时间', input_html: {'lay-verify': "required", class: 'design_in_at', autocomplete: 'off', value: f.object.design_in_at&.strftime("%Y-%m-%d %H:%M:%S")}, as: :string, placeholder: "请选择立项时间", readonly: true, unit: "&#xe637;" %>
  <%#= f.input :status, label: '状态', input_html: {'lay-verify': "required"} %>
  <%= f.input :mp_plus_six_months, label: 'MP+6月', input_html: {'lay-verify': "required"} %>
  <%= f.input :specification_file, label: '规格附件', input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>
  <div class="layui-form string required project_specification_file">
    <div class="layui-form-item">
      <label class="layui-form-label string required">规格附件</label>
      <div class="layui-input-block flex-item layui-input-wrap">
        <button type="button" class="layui-btn upload-project_specification_file" lay-options="{accept: 'file'}">
          <i class="layui-icon layui-icon-upload"></i>
          上传文件
        </button>
        <div class="layui-inline layui-word-aux file_current">
          <a class="down_template" href="/规格评估表.xlsx">下载模板</a>
        </div>
      </div>

    </div>
  </div>

  <%= f.input :user_id, label: '项目创建人', input_html: {'lay-verify': "required"}, wrapper_html: {style: 'display:none'} %>

  <div class="layui-form boolean optional project_agreement_accepted" style="padding: 0px 40px;">
    <div class="layui-form-item">
      <input type="checkbox" name="project[agreement_accepted]" id="agreement_accepted" lay-verify="required" lay-skin="primary" title="同意" <%= @project.agreement_accepted ? 'checked' : ''%>>
      <a href="#terms" target="_blank" style="position: relative; top: 6px; left: -15px;">
        <ins>用户协议</ins>
      </a>
    </div>
  </div>

  <div class="actions" style='display: none'>
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
    <%= link_to t('buttons.cancel'), url_for(:back), class: 'layui-btn layui-btn-normal' %>
  </div>
<% end %>

<script>
  BuildFroalaEditor('#project_description');
  var layer, form, pull_selected_data = true;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form', 'xmSelect'], function () {
      layer = layui.layer,
      form = layui.form;
      var upload = layui.upload;
      var laydate = layui.laydate;
      laydate.render({
        elem: '.design_in_at',
        type: 'datetime',
        fullPanel: true
      });

      laydate.render({
        elem: '.evt_at',
        type: 'datetime',
        fullPanel: true
      });

      laydate.render({
        elem: '.dvt_at',
        type: 'datetime',
        fullPanel: true
      });

      laydate.render({
        elem: '.pvt_at',
        type: 'datetime',
        fullPanel: true
      });

      laydate.render({
        elem: '.mp_at',
        type: 'datetime',
        fullPanel: true
      });

      laydate.render({
        elem: '.opening_at',
        type: 'datetime',
        fullPanel: true
      });

      upload.render({
        elem: '.upload-project_specification_file', // 绑定多个元素
        url: '/upload_anyfile', // 此处配置你自己的上传接口即可
        accept: 'file',
        size: 500000,
        multiple: true,
        done: function(res){
          console.log(res)
          $('.down_template').hide();
          $('#project_specification_file').val(res.link);
          var file_html = `<span >${res.file_name}<i class='layui-icon layui-icon-clear clear_file' style='color: #ff5722;'></i></span>`
          $('.file_current').html('');
          $('.file_current').append(file_html)
        },
        error: function(){ // 上传失败的回调
          layer.msg('文件上传失败', {icon: 2});
        }
      });

      form.on('radio(project_type)', function(data){
        var elem = data.elem; // 获得 radio 原始 DOM 对象
        var checked = elem.checked; // 获得 radio 选中状态
        var value = elem.value; // 获得 radio 值
        var othis = data.othis; // 获得 radio 元素被替换后的 jQuery 对象

        layer.msg(['value: '+ value, 'checked: '+ checked].join('<br>'));
      });

      form.on('radio(setname-radio-filter)', function(data){
        var elem = data.elem; // 获得 radio 原始 DOM 对象
        var checked = elem.checked; // 获得 radio 选中状态
        var value = elem.value; // 获得 radio 值
        var othis = data.othis; // 获得 radio 元素被替换后的 jQuery 对象
        if (value == 'auto') {
          $('#project_name').val('');
          $('.project_name').hide();
        } else {
          $('.project_name').show();
        }
      });

      form.on('select(chip_organization_id)', function(data){
        var elem = data.elem; // 获得 select 原始 DOM 对象
        var value = data.value; // 获得被选中的值
        var othis = data.othis; // 获得 select 元素被替换后的 jQuery 对象
        if (value.length > 0){

          $.ajax({
            url: '/admin/public_api/get_chip_configs',
            type: 'GET',
            data: {
              organization_id:  value
            },
            success: function (res) {
              selectChipOsSoftware('');
              layui.xmSelect.render({
                el: '#select_chip_config_id',
                name: 'project[chip_config_id]',
                autoRow: true,
                radio: true,
                toolbar: { show: true },
                tips: '请选择芯片平台',
                filterable: true,
                layVerify: 'required',
                remoteSearch: false,
                data: res.data,
                on: function (data) {
                  const selected = data.arr;
                  if (selected.length > 0) {
                      selectChipOsSoftware(selected[0].value);
                  } else {
                      selectChipOsSoftware('');
                  }
                }
              });
            }
          });
        }
      });

      form.render();
    });

    // 初始化 xmSelect 渲染器
    function renderXmSelect(el, name, tips, data, onCallback) {
        layui.xmSelect.render({
            el: el,
            name: name,
            autoRow: true,
            radio: true,
            toolbar: { show: true },
            tips: tips,
            filterable: true,
            layVerify: 'required',
            remoteSearch: false,
            data: data,
            on: onCallback
        });
    }

    // 渲染芯片配置选择器
    renderXmSelect(
        '#select_chip_config_id',
        'project[chip_config_id]',
        '请选择芯片平台',
        JSON.parse($('#select_chip_config_id').attr('data-value')),
        function (data) {
            const selected = data.arr;
            console.log('----请选择芯片平台----s')
            console.log(selected)
            if (selected.length > 0) {
                selectChipOsSoftware(selected[0].value);
            } else {
                selectChipOsSoftware('', true);
            }
        }
    );

    // 渲染 OS 软件选择器
    function selectChipOsSoftware(chipConfigId, change=false) {
        console.log("进入-----------OS");

        clearChipOsSoftwareSelect();
        if (chipConfigId) {
            fetchData('/admin/public_api/get_chip_sofwares', { chip_config_id: chipConfigId }, function (res) {
                renderXmSelect(
                    '#select_chip_os_software_id',
                    'project[chip_os_software_id]',
                    '请选择OS系统',
                    res.data,
                    null
                );
            });
        } else {
          renderXmSelect(
              '#select_chip_os_software_id',
              'project[chip_os_software_id]',
              '请选择OS系统',
              change ? [] : JSON.parse($('#select_chip_os_software_id').attr('data-value')),
              null
          );
        }
    }

    // 封装 AJAX 数据获取逻辑
    function fetchData(url, params, callback) {
        $.ajax({
            url: url,
            type: 'GET',
            data: params,
            success: function (res) {
                console.log(res);
                callback(res);
            },
            error: function (err) {
                console.error('数据加载失败:', err);
            }
        });
    }

    function clearChipOsSoftwareSelect() {
      layui.xmSelect.render({
        el: '#select_chip_os_software_id',
        name: 'project[chip_os_software_id]',
        autoRow: true,
        radio: true,
        toolbar: { show: true },
        tips: '请选择OS系统',
        filterable: true,
        layVerify: 'required',
        remoteSearch: false,
        data: [[]]  // 清空数据
      });
    }

    // 初始化清空状态
    selectChipOsSoftware('');
  });

  $(document).on('click', '.clear_file', function(){
    $(this).parent().remove();
    $('#project_specification_file').val('');
    $('.down_template').show();
  });
</script>