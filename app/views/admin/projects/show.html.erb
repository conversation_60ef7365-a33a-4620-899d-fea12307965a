<div class="layui-card">
  <div class="layui-card-body">
    <%= render 'admin/projects/project_tab' %>
    <div style="padding: 20px;">

      <div class="flex-item-around">
        <h4><%= @project.name %></h4>
        <div>
          <% if @project.progressing? %>
            <button class="layui-btn layui-btn-primary layui-border-orange layui-btn-sm" id="release_pending" data-status="pending">挂起</button>
            <button class="layui-btn layui-btn-primary layui-border-orange layui-btn-sm" id="release_closed" data-status="closed">结项</button>
          <% elsif @project.pending? %>
            <button class="layui-btn layui-btn-primary layui-border-orange layui-btn-sm" id="release_progressing" data-status="progressing">恢复</button>
            <button class="layui-btn layui-btn-primary layui-border-orange layui-btn-sm" id="release_abnormal_closed" data-status="abnormal_closed">异常结项</button>
          <% end %>

          <% if @project.editable? %>
            <button class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm" id="edit" data-type="<%= @project.project_type %>">编辑</button>
          <% end %>
          <button class="layui-btn layui-btn-primary layui-border-red layui-btn-sm" id="deleted">删除</button>
        </div>
      </div>
      <hr>

        <!-- 项目计划属性 -->
        <div class="layui-bg-gray" style="padding: 16px;">
        <div class="layui-row layui-col-space15">
          <div class="layui-col-md3">
            <div class="layui-card">
              <div class="layui-card-header">项目进度</div>
              <div class="layui-card-body">
                <div class="project-show-title"><span class="project-show-number"><%= @progress_rate %></span>%</div>
                <div class="layui-progress" style="margin-top: 3px;">
                  <div class="layui-progress-bar layui-bg-red" lay-percent="<%= @progress_rate %>"></div>
                </div>
                <span class="font-11" style="color: #ff5722;">延期: <%= @delay_rate %>%</span>
                <!-- <span class="font-11">计划: 0%</span> -->
              </div>
            </div>
          </div>

          <% if @project.p_joint? || @project.p_internal? %>
            <div class="layui-col-md3">
              <div class="layui-card">
                <a href="/admin/plans?project_id=<%= @project.id %>">
                  <div class="layui-card-header">任务</div>
                  <div class="layui-card-body">
                    <div class="project-show-title"><span class="project-show-number"><%= @project.plans.count %></span>个</div>
                    <div class="layui-progress" style="margin-top: 3px;">
                      <div class="layui-progress-bar layui-bg-blue" lay-percent="<%= @project.plan_progress %>%"></div>
                    </div>
                    <span class="font-11" style="color: #16b777;">已完成: <%= @project.plans.completed.count %>个</span>
                    <span class="font-11" style="color: #1e9fff;">未完成: <%= @project.plans.where(status: ['no_start', 'in_progress']).count %></span>
                    <span class="font-11" style="color: #ffb800;">中止: <%= @project.plans.discontinued.count%></span>
                    <span class="font-11" style="color: #ff5722;">延期: <%= @project.delay_risks.count %></span>
                  </div>
                </a>
              </div>
            </div>
          <% end %>

          <% if @project.p_support? || @project.p_joint? %>
            <div class="layui-col-md3">
              <div class="layui-card">
                <a href="<%= project_bug_works_admin_work_orders_path(project_id: @project.id) %>">
                  <div class="layui-card-header">缺陷</div>
                  <div class="layui-card-body">
                    <div class="project-show-title"><span class="project-show-number"><%= @project.work_orders.where(work_type: 'bug').count %></span>个</div>
                    <div class="layui-progress" style="margin-top: 3px;">
                      <div class="layui-progress-bar layui-bg-orange" lay-percent="<%= @project.work_progress('bug') %>%"></div>
                    </div>
                    <span class="font-11" style="color: #16b777;">已解决: <%= @project.work_orders.where(work_type: 'bug', aasm_state: ['close', 'solved']).count %>个</span>
                    <span class="font-11" style="color: #1e9fff;">未解决: <%= @project.plans.where(status: ['no_start', 'in_progress']).count %></span>
                    <span class="font-11" style="color: #ff5722;">延期: <%= WorkOrder.where_delay_work_order(@project.id, 'bug').count %>个</span>
                  </div>
                </a>
              </div>
            </div>

            <div class="layui-col-md3">
              <div class="layui-card">
                <a href="<%= project_demand_works_admin_work_orders_path(project_id: @project.id) %>">
                  <div class="layui-card-header">需求</div>
                  <div class="layui-card-body">
                    <div class="project-show-title"><span class="project-show-number"><%= @project.work_orders.where(work_type: 'demand').count %></span>个</div>
                    <div class="layui-progress" style="margin-top: 3px;">
                      <div class="layui-progress-bar layui-bg-primary" lay-percent="<%= @project.work_progress('demand') %>%"></div>
                    </div>

                    <span class="font-11" style="color: #16b777;">已解决: <%= @project.work_orders.where(work_type: 'demand', aasm_state: ['close', 'solved']).count %>个</span>
                    <span class="font-11" style="color: #1e9fff;">未解决: <%= @project.plans.where(status: ['no_start', 'in_progress']).count %></span>
                    <span class="font-11" style="color: #ff5722;">延期: <%= WorkOrder.where_delay_work_order(@project.id, 'demand').count %>个</span>
                  </div>
                </a>
              </div>
            </div>
          <% elsif @project.p_internal? %>
            <div class="layui-col-md3">
              <div class="layui-card">
                <a href="<%= project_bug_works_admin_work_orders_path(project_id: @project.id) %>">
                  <div class="layui-card-header">缺陷</div>
                  <div class="layui-card-body">
                    <div class="project-show-title"><span class="project-show-number"><%= @project.work_orders.where(work_type: 'bug').count %></span>个</div>
                    <div class="layui-progress" style="margin-top: 3px;">
                      <div class="layui-progress-bar layui-bg-orange" lay-percent="<%= @project.work_progress('bug') %>%"></div>
                    </div>
                    <span class="font-11" style="color: #16b777;">已解决: <%= @project.work_orders.where(work_type: 'bug', aasm_state: ['close', 'solved']).count %>个</span>
                    <span class="font-11" style="color: #1e9fff;">未解决: <%= @project.plans.where(status: ['no_start', 'in_progress']).count %></span>
                    <span class="font-11" style="color: #ff5722;">延期: <%= WorkOrder.where_delay_work_order(@project.id, 'bug').count %>个</span>
                  </div>
                </a>
              </div>
            </div>
          <% end %>
          <div class="layui-col-md3">
            <div class="layui-card">
              <a href="<%= admin_project_risks_path(project_id: @project.id) %>">
                <div class="layui-card-header">风险</div>
                <div class="layui-card-body">
                  <div class="project-show-title"><span class="project-show-number"><%= @project.project_risks.count %></span>个</div>
                  <div class="layui-progress" style="margin-top: 3px;">
                    <div class="layui-progress-bar layui-bg-orange" lay-percent="<%= @project.risk_progress %>%"></div>
                  </div>
                  <span class="font-11" style="color: #16b777;">已解决: <%= @project.project_risks.where(aasm_state: ['converted', 'closed']).count %>个</span>
                  <span class="font-11" style="color: #1e9fff;">未解决: <%= @project.project_risks.where(aasm_state: ['identified', 'tracked']).count %></span>
                  <span class="font-11" style="color: #ff5722;">延期: <%= WorkOrder.where_delay_work_order(@project.id, 'bug').count %>个</span>
                </div>
              </a>
            </div>
          </div>

        </div>
      </div>

      <div class="layui-tab layui-tab-brief">
        <ul class="layui-tab-title">
          <li class="layui-this">基本信息</li>
          <li>里程碑</li>
          <li>申请流程进展</li>
        </ul>
        <div class="layui-tab-content">
          <div class="layui-tab-item layui-show">
            <div class="flex show-detail">
              <table class="layui-table" lay-skin="nob">
                <colgroup>
                  <col width="120">
                  <col width="40%">
                  <col width="120">
                  <col>
                </colgroup>

                <tbody>
                  <tr>
                    <th>项目名称:</th>
                    <td><%= @project.name %></td>

                    <th>开案说明:</th>
                    <td><%= @project.opening_desc %></td>
                  </tr>

                  <tr>
                    <th>项目人数:</th>
                    <td><%= @project.team_size %></td>

                    <th>项目类型:</th>
                    <td><%= @project.project_type_i18n %></td>
                  </tr>

                  <tr>
                    <th>业务受理人:</th>
                    <td><%= @project.acceptor_name %></td>

                    <th>规格附件:</th>
                    <td><%= @project.specification_file.to_filename %></td>
                  </tr>

                  <tr>
                    <th>客户项目简称:</th>
                    <td><%= @project.customer_project_name %></td>

                    <th>立项时间:</th>
                    <td><%= @project.design_in_at&.strftime('%F %T') %></td>
                  </tr>

                  <tr>
                    <th>DVT时间:</th>
                    <td><%= @project.dvt_at&.strftime('%F %T') %></td>

                    <th>PVT时间:</th>
                    <td><%= @project.pvt_at&.strftime('%F %T') %></td>
                  </tr>

                  <tr>
                    <th>EVT时间:</th>
                    <td><%= @project.evt_at&.strftime('%F %T') %></td>

                    <th>PVT时间:</th>
                    <td><%= @project.pvt_at&.strftime('%F %T') %></td>
                  </tr>

                  <tr>
                    <th>MP时间:</th>
                    <td><%= @project.mp_at&.strftime('%F %T') %></td>

                    <th>状态:</th>
                    <td><%= @project.status_i18n %></td>
                  </tr>

                  <tr>
                    <th>FCST/月:</th>
                    <td><%= @project.fcst_per_month %></td>

                    <th>目标市场区域:</th>
                    <td><%= @project.target_market_region %></td>
                  </tr>

                  <tr>
                    <th>产品主要功能:</th>
                    <td><%= @project.main_competitiveness %></td>

                    <th>终端客户简称:</th>
                    <td><%= @project.terminal_customer_name %></td>
                  </tr>

                  <tr>
                    <th>MP+6个月:</th>
                    <td><%= @project.mp_plus_six_months %></td>

                    <th>业务负责人:</th>
                    <td><%= @project.business_contact&.name %></td>
                  </tr>


                  <tr>
                    <th>参与企业</th>
                    <td>归属: <%= @project.main_project_organization.organization.name %> 参与: <%= @project.member_organizations %></td>

                    <th>芯片来源:</th>
                    <td><%= @project.chip_organization.name %></td>
                  </tr>

                  <tr>
                    <th>开案时间:</th>
                    <td><%= @project.opening_at&.strftime('%F %T') %></td>

                    <th>芯片平台:</th>
                    <td><%= @project.chip_config&.name %></td>
                  </tr>

                  <tr>
                    <th>产品名称:</th>
                    <td><%= @project.product_name %></td>


                    <th>OS系统:</th>
                    <td><%= @project.chip_os_software&.name %></td>
                  </tr>

                  <tr>
                    <th>创建人:</th>
                    <td><%= @project.user&.name %></td>

                    <th>软件版本号:</th>
                    <td><%= @project.chip_os_version&.version %></td>
                  </tr>

                  <tr>
                    <th>产品类型:</th>
                    <td><%= @project.product_category&.name %></td>

                    <th>产品类型:</th>
                    <td><%= @project.product_category&.name %></td>
                  </tr>


                  <tr>
                    <th>产品经理:</th>
                    <td><%= @project.product_manager&.name %></td>

                    <th>技术经理:</th>
                    <td><%= @project.technical_manager&.name %></td>
                  </tr>

                  <tr>
                    <th>项目描述:</th>
                    <td><%= @project.description %></td>

                    <th>主要目的:</th>
                    <td><%= @project.main_purpose %></td>
                  </tr>
                </tbody>
              </table>


            </div>
          </div>
          <div class="layui-tab-item">
            <div class="layui-bg-gray" style="padding: 16px;">
              <% if @project.plans.p_milestone.count > 0 %>
                <div class="progress-steps">
                  <% @project.plans.p_milestone.order(:order_number).each_with_index do |plan, i| %>
                    <% steps = plan.step_html(i+1) %>
                    <div class="step <%= steps[0] %>">
                      <div class="circle"><%= steps[1].html_safe %></div>

                      <div class="label"><%= plan.name %></div>
                    </div>
                  <% end %>
                </div>
              <% else %>
                无里程碑信息
              <% end %>
            </div>
          </div>
          <div class="layui-tab-item">
            <% if @approval_flow %>
              <%= render 'admin/approval_flows/show' %>
            <% end %>
          </div>
        </div>
      </div>

    </div>
  </div>
</div>

<script>
  $(document).on('click', '#edit', function (){
    var type = $(this).attr('data-type');
    var id = `<%= @project.id %>`;
    if (type == 'p_internal'){ // 内部方案
      $.ajax({
        type: 'GET',
        url: `/admin/projects/${id}/edit`,
        data: {
        }
      })
    }else if (type == 'p_support'){ // 技术支持案
      $.ajax({
        type: 'GET',
        url: `/admin/projects/edit_support`,
        data: {
          id: id
        }
      })
    }else if (type == 'p_joint'){ // 联合开发案
      $.ajax({
        type: 'GET',
        url: `/admin/projects/edit_joint`,
        data: {
          id: id
        }
      })
    }
  })

  $(document).on('click', '#deleted', function (){
    var id = `<%= @project.id %>`;

    layer.prompt({title: '请输入项目名称“<%= @project.name %>”，用于确认删除', move: false}, function(value, index, elem){
      if(value === '') return elem.focus();
      // 关闭 prompt
      layer.close(index);
      $.ajax({
        type: 'DELETE',
        url: `/admin/projects/${id}`,
        data: {
          'confirm_name': value
        }
      })
    });
  })

  $(document).on('click', '#release_pending, #release_closed, #release_progressing, #release_abnormal_closed', function (){
    var id = `<%= @project.id %>`;
    var status = $(this).attr('data-status');
    var title = '';
    if (status == 'pending'){
      title = '确认挂起项目吗？'
    }else if (status == 'closed'){
      title = '确认结项吗？'
    }else if (status == 'progressing'){
      title = '确认恢复项目吗？'
    }else if (status == 'abnormal_closed'){
      title = '确认异常结项吗？'
    }

    layer.confirm(title, function(index){
      layer.close(index);
      $.ajax({
        type: 'POST',
        url: `/admin/projects/update_status`,
        data: {
          id: id,
          status: status
        },success: function(res){
          window.location.reload();
        }
      })
    })
  })

  $(document).on('click', '.show-detail-title', function(){
    $(this).find('i').toggleClass('layui-icon-down');
    $(this).find('i').toggleClass('layui-icon-up');

    $('.show-detail').toggle($(this).find('i').hasClass('layui-icon-down'));
  })


</script>