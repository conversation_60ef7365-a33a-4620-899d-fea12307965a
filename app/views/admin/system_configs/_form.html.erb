<%= simple_form_for([:admin, @system_config], wrapper: :seven_form, html: {id: 'user_form', class: 'layui-form', multipart: true, novalidate: 'novalidate' }) do |f| %>
  <%= f.error_notification %>

  <div class="layui-tab layui-tab-brief">
    <ul class="layui-tab-title">
      <li class="layui-this">邮箱配置</li>
      <li>飞书消息</li>

    </ul>
    <div class="layui-tab-content">

      <div class="layui-tab-item layui-show">
        <%= f.input :email_status, label: '是否启用' %>
        <%= f.input :email_address, label: '邮箱地址' %>
        <%= f.input :password, label: '邮箱密码（保存后将被隐藏）' %>
        <%= f.input :smtp_server, label: 'SMTP服务器地址' %>
        <%= f.input :port, label: 'SMTP服务器端口' %>
        <%= f.input :authentication, label: 'SMTP认证方式' %>
        <%= f.input :enable_tls, label: '是否启用TLS加密' %>
        <%= f.input :enable_ssl, label: '是否启用SSL加密' %>
        <%= f.input :domain, label: 'SMTP域名' %>
      </div>

      <div class="layui-tab-item">
        <%= f.input :feishu_status, label: '是否启用' %>
        <%= f.input :feishu_app_id, label: '飞书 AppId' %>
        <%= f.input :feishu_app_secret, label: '飞书 AppSecret' %>
      </div>

    </div>
  </div>

    <%#= f.input :ding_agent_id, label: '钉钉 AgentId' %>
    <%#= f.input :ding_app_key, label: '钉钉 AppKey' %>
    <%#= f.input :ding_app_secret, label: '钉钉 AppSecret' %>
    <%#= f.input :qiye_app_id, label: '企微 AppId' %>
    <%#= f.input :qiye_agent_id, label: '企微 AgentId' %>
    <%#= f.input :qiye_secret, label: '企微 Secret' %>

  <div class="actions">
    <%= f.submit t('buttons.save'), data: { disable_with: "保存中..." }, class: 'layui-btn save_btn', 'lay-submit': '' %>
  </div>
<% end %>

<script>
  var layer, form;//保存layui模块以便全局使用
  $(function(){
    //加载&&初始化layui模块
    layui.use(['layer', 'form'], function () {
      layer = layui.layer,
      form = layui.form;
      form.render();
    });
  });
</script>