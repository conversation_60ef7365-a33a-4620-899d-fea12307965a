<div class="layui-card">
  <div class="layui-card-body">
    <table id="table_demo" lay-filter="admin-table"></table>
  </div>
</div>

<script type="text/html" id="myBar">
  <button lay-event="checked_read" type="button" class="layui-btn layui-btn-sm">标记已读</button>
  <button lay-event="all_read" type="button" class="layui-btn layui-btn-sm">全部已读</button>
</script>

<script>
  var table;
  layui.use(function(){
  table = layui.table
  var layer = layui.layer //弹层
    ,laypage = layui.laypage //分页
    ,limit = 10;//分页大小全局变量

    table.render({
      id: 'user_notice'
      ,elem: '#table_demo'
      ,url: `/admin/user_notices` //数据接口
      ,title: '列表'
      ,page: true //开启分页
      ,limit: limit
      ,skin: 'line'
      ,toolbar: '#myBar'
      ,cols: [[
        {type: 'checkbox', fixed: 'left'},
        {field: 'id', align:'left', title: 'ID', hide: true},
        {field: 'name', align:'left', title: '标题', width: 200, templet: function(d) {
          console.log(d.url_open_way)
          if (d.url_open_way == '') {
            return d.name
          }else if (d.url_open_way == 'new_windows'){
            return `<a style="color: #2468f2;" lay-href='${d.url}' lay-title=${d.name}>${d.name}</a>`
          }else if (d.url_open_way == 'remote'){
            return `<a style="color: #2468f2;" href='${d.url}' data-remote='true'>${d.name}</a>`
          }

        }},
        {field: 'content', align:'left', title: '内容', minWidth: 300},
        {field: 'created_at', align:'left', title: '创建时间', width: 180},
        {field: 'status', align:'left', title: '状态', width: 100, templet: function(d) {
          if (d.status){
            return `<span class='layui-badge layui-bg-gray' >已读</span>`
          }else{
            return `<span class='layui-badge layui-bg-orange'>未读</span>`
          }
        }}

      ]]
    });

    //监听头工具栏事件
    table.on('toolbar(admin-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
      var ids = [];
      switch(obj.event){
        case 'checked_read':
          $.each(checkStatus.data, function(index, item) {
            ids.push(item.id);
          });
          if (ids.length == 0){
            layer.msg('没有选择任何通知消息')
            return
          }
          $.ajax({
            type: 'PUT',
            url: `/admin/user_notices/${ids}`,
            data: {
            },success:function(){
              ids = []
            }
          })
        break;

        case 'all_read':
          var getData = table.getData(obj.config.id);
          $.each(getData, function(index, item) {
            ids.push(item.id);
          });

          if (ids.length == 0){
            return
          }

          $.ajax({
            type: 'PUT',
            url: `/admin/user_notices/${ids}`,
            data: {
            },success:function(){
              ids = []
            }
          })
        break;
      };
    });
  })
</script>