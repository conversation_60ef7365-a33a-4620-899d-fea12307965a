<div class="layui-card">
  <div class="layui-card-body">
    <table id="table_groups" lay-filter="admin-table"></table>
  </div>
</div>

<script type="text/html" id="barDemo">
  <a class="layui-btn layui-btn-xs layui-border-blue layui-btn-primary" lay-event="detail" title="查看成员"> <i class="layui-icon layui-icon-user"></i></a>
  <a class="layui-btn layui-btn-xs layui-border-green layui-btn-primary" lay-event="edit" title="编辑"> <i class="layui-icon layui-icon-edit"></i></a>
  <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted" title="删除"> <i class="layui-icon layui-icon-delete"></i></a>
</script>
<script type="text/html" id="myBar">
  <button lay-event="add" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>新增用户组</button>
</script>

<script>
  var table;
  layui.use(function(){
  table = layui.table
  var layer = layui.layer //弹层
    ,laypage = layui.laypage //分页
    ,page = 1//页码全局变量
    ,limit = 10;//分页大小全局变量

    table.render({
      id: 'listPage',
      elem: '#table_groups',
      url: `/admin/groups`, //数据接口
      title: '用户组列表',
      page: true, //开启分页
      skin: 'line',
      limit: limit,
      toolbar: '#myBar',
      cols: [[
        {field: 'id', align:'left', title: 'ID', hide: true},
        {field: 'name', align:'left', title: '用户组名称', minWidth: 150},
        {field: 'description', align:'left', title: '描述', minWidth: 200},
        {field: 'users_count', align:'center', title: '成员数量', width: 120},
        {field: 'created_at', align:'center', title: '创建时间', width: 180},
        {fixed: 'right', title: '操作', minWidth: 200, align: 'center', toolbar: '#barDemo'}
      ]]
    });

    //监听行工具事件
	  table.on('tool(admin-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if(layEvent === 'edit'){
        $.ajax({
          type: 'GET',
          url: `/admin/groups/${data.id}/edit`,
          data: {
          }
        })
      }else if (layEvent === 'detail'){
        // 查看组成员
        layer.open({
          type: 2,
          title: `${data.name} - 组成员管理`,
          shadeClose: true,
          shade: 0.8,
          area: ['80%', '70%'],
          content: `/admin/groups/${data.id}`
        });
      } else if (layEvent === 'deleted'){
        layer.confirm('确认删除吗？此操作不可恢复', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/groups/${data.id}`,
            data: {
            }
          })
        })
      }
	  });

    //监听头工具栏事件
    table.on('toolbar(admin-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
      ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/groups/new`,
            data: {
            }
          })
        break;
      };
    });

  })
</script>
