

<div class="layui-card">
  <div class="layui-card-body">
    <%= render 'admin/projects/project_tab' %>

    <div class="layui-tabs-body" style="padding: 16px;">
      <div class="layui-tabs-item layui-show">
        <div class="layui-tabs" lay-options="{headerMode:'normal'}" id="project_organization_tabs">
          <ul class="layui-tabs-header">
            <% @project.project_organizations.order(id: :asc).each do |project_organization| %>
              <li data-organization_id="<%= project_organization.organization_id %>" class="<%= project_organization.organization_id == current_organization_id ? 'layui-this' : ''%>"><%= project_organization.organization.name %></li>
            <% end %>
          </ul>
          <div class="layui-tabs-body">
            <div class="layui-tabs-item layui-show"><table id="table_project_users" lay-filter="admin-table"></table></div>
          </div>
        </div>
      </div>
    </div>

  </div>
</div>
<% if (@project_power_keys.include?('set_member') && current_user.organization.parent_id.blank?) || (current_user.organization.parent_id.blank? && current_user.is_admin) %>
  <script type="text/html" id="barDemo">
    <a class="layui-btn layui-btn-xs layui-border-red layui-btn-primary" lay-event="deleted"> <i class="layui-icon layui-icon-delete"></i> </a>
  </script>
  <script type="text/html" id="myBar">
    <button lay-event="add" type="button" class="layui-btn layui-btn-primary layui-border-blue layui-btn-sm"><i class="layui-icon layui-icon-add-1"></i>新增</button>
  </script>
<% end %>
<script>
  var table;
  var select_organization_id = `<%= current_organization_id %>`
  layui.use(function(){
  table = layui.table
  var layer = layui.layer //弹层
  ,laypage = layui.laypage //分页
  var tabs = layui.tabs;

    table.render({
      id: 'listPage',
      elem: '#table_project_users',
      url: `/admin/project_users?project_id=<%= @project.id %>`, //数据接口
      where: {
        organization_id: select_organization_id
      },
      title: '列表',
      skin: 'line',
      page: false,
      toolbar: '#myBar',
      cols: [[
        {field: 'user_name', align:'center', title: '角色', width: 180 },
        {field: 'role_name', align:'center', title: '权限', minWidth: 300, templet: function (d)
          {
            html = ''
            d.role_name.forEach(function(res){
              html += `<div class="layui-badge set_tag_type layui-bg-blue" style='margin-left: 2px; '>${res[0]}<a href='javascript:void(0)' data-id='${res[1]}' class='close_role'><i class='layui-icon layui-icon-close' style='font-size: 14px; margin-left: 9px;'></i></a> </div>`
            })
            html += `<i class='layui-icon layui-icon-edit edit_role' data-id='${d.user_id}' style='margin-left: 10px;'></i>`
            return html
          }
        },
        {fixed: 'right', title: '操作', minWidth: 170, align: 'left', toolbar: '#barDemo'}
      ]]
    });

    //监听行工具事件
	  table.on('tool(admin-table)', function(obj){ //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
      var data = obj.data //获得当前行数据
      ,layEvent = obj.event; //获得 lay-event 对应的值
      if (layEvent === 'deleted'){
        layer.confirm('确认删除吗？此操作不可恢复', function(index){
          layer.close(index);
          $.ajax({
            type: 'DELETE',
            url: `/admin/project_users/${data.id}`,
            data: {
              user_id: data.user_id,
              project_id: '<%= @project.id %>',
              organization_id: select_organization_id
            }
          })
        })
      }

	  });

    //监听头工具栏事件
    table.on('toolbar(admin-table)', function(obj){
      var checkStatus = table.checkStatus(obj.config.id)
      ,data = checkStatus.data; //获取选中的数据
      switch(obj.event){
        case 'add':
          $.ajax({
            type: 'GET',
            url: `/admin/project_users/new`,
            data: {
              project_id: `<%= @project.id %>`,
              organization_id: select_organization_id
            }
          })
        break;
      };
    });
  })

  $(document).on('click', '.close_role', function(){
    var id = $(this).attr('data-id')
    layer.confirm('确认删除吗？删除后改成员不再有此权限', function(index){
      $.ajax({
        type: 'POST',
        url: '/admin/project_users/delete_role',
        data: {
          id: id,
          project_id: '<%= @project.id %>',
          organization_id: select_organization_id
        }
      })
      layer.close(index);
      //向服务端发送删除指令
    });
  })

  $(document).on('click', '.edit_role', function(){
    var user_id = $(this).attr('data-id');
    $.ajax({
      type: 'GET',
      url: `/admin/project_users/${user_id}/edit`,
      data: {
        user_id: user_id,
        project_id: '<%= @project.id %>',
        organization_id: select_organization_id
      }
    })
  })

  $(document).on('click', '#project_organization_tabs li', function(){
    var organization_id = $(this).attr('data-organization_id');
    table.reload('listPage', {
      where: {
        organization_id: organization_id
      }
    });
    select_organization_id = organization_id
  })
</script>