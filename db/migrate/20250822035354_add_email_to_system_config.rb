class AddEmailToSystemConfig < ActiveRecord::Migration[7.0]
  def change
    add_column :system_configs, :email_address, :string, comment: '邮箱地址'
    add_column :system_configs, :password, :string, comment: '邮箱密码'
    add_column :system_configs, :smtp_server, :string, comment: 'SMTP服务器地址'
    add_column :system_configs, :port, :integer, comment: 'SMTP服务器端口'
    add_column :system_configs, :authentication, :string, default: "plain", comment: 'SMTP认证方式'
    add_column :system_configs, :enable_tls, :boolean, default: true, comment: '是否启用TLS加密'
    add_column :system_configs, :enable_ssl, :boolean, default: false, comment: '是否启用SSL加密'
    add_column :system_configs, :domain, :string, comment: 'SMTP域名'
    add_column :system_configs, :feishu_status, :boolean, default: false, comment: '是否启用飞书'
    add_column :system_configs, :qiye_status, :boolean, default: false, comment: '是否启用企微'
    add_column :system_configs, :ding_status, :boolean, default: false, comment: '是否启用钉钉'
    add_column :system_configs, :email_status, :boolean, default: false, comment: '是否启用邮件'
  end
end
