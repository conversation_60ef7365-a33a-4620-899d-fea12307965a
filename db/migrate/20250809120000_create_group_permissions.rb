class CreateGroupPermissions < ActiveRecord::Migration[7.0]
  def change
    create_table :group_permissions do |t|
      t.integer :group_id, comment: "用户组ID"
      t.integer :permission_action_id, comment: "权限动作ID"
      t.integer :organization_id, comment: "组织ID"
      t.datetime :deleted_at, comment: "删除时间"

      t.timestamps
    end

    add_index :group_permissions, :group_id
    add_index :group_permissions, :permission_action_id
    add_index :group_permissions, :organization_id
    add_index :group_permissions, :deleted_at
  end
end
