desc '检查延期任务, 检查即将延期任务'
task check_tasks: :environment do
  p '--------- 检查任务开始 ---------'
  User.find_each do |user|
    # 定义任务类型和关联方法映射
    task_types = {
      delay: {
        label: "已延期任务",
        associations: [
          { name: "项目任务", assoc: :delay_plans },
          { name: "工单任务", assoc: :delay_work_orders },
          { name: "风险任务", assoc: :delay_risks }
        ]
      },
      will_delay: {
        label: "即将延期任务",
        associations: [
          { name: "项目任务", assoc: :will_delay_plans },
          { name: "工单任务", assoc: :will_delay_work_orders },
          { name: "风险任务", assoc: :will_delay_risks }
        ]
      }
    }

    # 收集用户的所有任务
    all_tasks = []
    task_types.each do |status, config|
      config[:associations].each do |assoc|
        names = user.send(assoc[:assoc]).map(&:name)
        next if names.empty?

        all_tasks << {
          type: assoc[:name],
          tasks: names,
          status: config[:label]
        }
      end
    end

    # 如果没有任务，跳过该用户
    next if all_tasks.empty?

    # 动态生成提醒内容
    content = "延期、即将延期任务提醒：\n\n"
    all_tasks.group_by { |t| t[:status] }.each do |status_label, tasks|
      content += "**#{status_label}**:\n"
      tasks.group_by { |t| t[:type] }.each do |type_name, items|
        content += "  - **#{type_name}**: #{items.map { |i| i[:tasks].join("、") }.join("\n")}\n"
      end
    end

    p "用户 #{user.name} 的任务提醒内容: #{content}"
    # 发送通知
    UserNotice.init_message!(
      user.organization_id,
      user.id,
      user,
      "延期、即将延期任务提醒",
      content,
      nil,
      nil
    )
  end
end