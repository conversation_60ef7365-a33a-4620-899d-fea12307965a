GEM
  remote: https://gems.ruby-china.com/
  specs:
    aasm (5.5.0)
      concurrent-ruby (~> 1.0)
    actioncable (7.0.6)
      actionpack (= 7.0.6)
      activesupport (= 7.0.6)
      nio4r (~> 2.0)
      websocket-driver (>= 0.6.1)
    actionmailbox (7.0.6)
      actionpack (= 7.0.6)
      activejob (= 7.0.6)
      activerecord (= 7.0.6)
      activestorage (= 7.0.6)
      activesupport (= 7.0.6)
      mail (>= 2.7.1)
      net-imap
      net-pop
      net-smtp
    actionmailer (7.0.6)
      actionpack (= 7.0.6)
      actionview (= 7.0.6)
      activejob (= 7.0.6)
      activesupport (= 7.0.6)
      mail (~> 2.5, >= 2.5.4)
      net-imap
      net-pop
      net-smtp
      rails-dom-testing (~> 2.0)
    actionpack (7.0.6)
      actionview (= 7.0.6)
      activesupport (= 7.0.6)
      rack (~> 2.0, >= 2.2.4)
      rack-test (>= 0.6.3)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.0, >= 1.2.0)
    actiontext (7.0.6)
      actionpack (= 7.0.6)
      activerecord (= 7.0.6)
      activestorage (= 7.0.6)
      activesupport (= 7.0.6)
      globalid (>= 0.6.0)
      nokogiri (>= 1.8.5)
    actionview (7.0.6)
      activesupport (= 7.0.6)
      builder (~> 3.1)
      erubi (~> 1.4)
      rails-dom-testing (~> 2.0)
      rails-html-sanitizer (~> 1.1, >= 1.2.0)
    activejob (7.0.6)
      activesupport (= 7.0.6)
      globalid (>= 0.3.6)
    activemodel (7.0.6)
      activesupport (= 7.0.6)
    activerecord (7.0.6)
      activemodel (= 7.0.6)
      activesupport (= 7.0.6)
    activestorage (7.0.6)
      actionpack (= 7.0.6)
      activejob (= 7.0.6)
      activerecord (= 7.0.6)
      activesupport (= 7.0.6)
      marcel (~> 1.0)
      mini_mime (>= 1.1.0)
    activesupport (7.0.6)
      concurrent-ruby (~> 1.0, >= 1.0.2)
      i18n (>= 1.6, < 2)
      minitest (>= 5.1)
      tzinfo (~> 2.0)
    addressable (2.8.4)
      public_suffix (>= 2.0.2, < 6.0)
    admin_help (0.1.5)
      simple_form
    airbrussh (1.4.1)
      sshkit (>= 1.6.1, != 1.7.0)
    aliyun-cloud_sms (0.2.3)
      rest-client (>= 2.0)
      uuid (>= 2.0)
    annotate (3.2.0)
      activerecord (>= 3.2, < 8.0)
      rake (>= 10.4, < 14.0)
    base64 (0.2.0)
    bcrypt (3.1.19)
    bigdecimal (3.1.9)
    bindex (0.8.1)
    bootsnap (1.16.0)
      msgpack (~> 1.2)
    builder (3.2.4)
    capistrano (3.17.1)
      airbrussh (>= 1.0.0)
      i18n
      rake (>= 10.0.0)
      sshkit (>= 1.9.0)
    capistrano-bundler (2.1.0)
      capistrano (~> 3.1)
    capistrano-rails (1.6.2)
      capistrano (~> 3.1)
      capistrano-bundler (>= 1.1, < 3)
    capistrano-rbenv (2.2.0)
      capistrano (~> 3.1)
      sshkit (~> 1.3)
    capistrano3-puma (6.0.0.beta.1)
      capistrano (~> 3.7)
      capistrano-bundler
      puma (>= 5.1, < 7.0)
    capybara (3.39.2)
      addressable
      matrix
      mini_mime (>= 0.1.3)
      nokogiri (~> 1.8)
      rack (>= 1.6.0)
      rack-test (>= 0.6.3)
      regexp_parser (>= 1.5, < 3.0)
      xpath (~> 3.2)
    carrierwave (3.0.0)
      activemodel (>= 6.0.0)
      activesupport (>= 6.0.0)
      addressable (~> 2.6)
      image_processing (~> 1.1)
      marcel (~> 1.0.0)
      ssrf_filter (~> 1.0)
    caxlsx (4.2.0)
      htmlentities (~> 4.3, >= 4.3.4)
      marcel (~> 1.0)
      nokogiri (~> 1.10, >= 1.10.4)
      rubyzip (>= 1.3.0, < 3)
    caxlsx_rails (0.6.4)
      actionpack (>= 3.1)
      caxlsx (>= 3.0)
    chronic (0.10.2)
    code_analyzer (0.5.5)
      sexp_processor
    concurrent-ruby (1.2.2)
    config (5.5.2)
      deep_merge (~> 1.2, >= 1.2.1)
      ostruct
    crass (1.0.6)
    date (3.3.3)
    debug (1.8.0)
      irb (>= 1.5.0)
      reline (>= 0.3.1)
    deep_cloneable (3.2.1)
      activerecord (>= 3.1.0, < 9)
    deep_merge (1.2.2)
    domain_name (0.6.20240107)
    enum_help (0.0.19)
      activesupport (>= 3.0.0)
    erubi (1.12.0)
    erubis (2.7.0)
    event_emitter (0.2.6)
    ffi (1.15.5)
    globalid (1.1.0)
      activesupport (>= 5.0)
    htmlentities (4.3.4)
    http-accept (1.7.0)
    http-cookie (1.0.8)
      domain_name (~> 0.5)
    httparty (0.18.1)
      mime-types (~> 3.0)
      multi_xml (>= 0.5.2)
    i18n (1.14.1)
      concurrent-ruby (~> 1.0)
    image_processing (1.12.2)
      mini_magick (>= 4.9.5, < 5)
      ruby-vips (>= 2.0.17, < 3)
    importmap-rails (1.2.1)
      actionpack (>= 6.0.0)
      railties (>= 6.0.0)
    io-console (0.6.0)
    irb (1.7.1)
      reline (>= 0.3.0)
    jbuilder (2.11.5)
      actionview (>= 5.0.0)
      activesupport (>= 5.0.0)
    jquery-rails (4.6.0)
      rails-dom-testing (>= 1, < 3)
      railties (>= 4.2.0)
      thor (>= 0.14, < 2.0)
    json (2.6.3)
    jwt (2.10.1)
      base64
    kaminari (1.2.2)
      activesupport (>= 4.1.0)
      kaminari-actionview (= 1.2.2)
      kaminari-activerecord (= 1.2.2)
      kaminari-core (= 1.2.2)
    kaminari-actionview (1.2.2)
      actionview
      kaminari-core (= 1.2.2)
    kaminari-activerecord (1.2.2)
      activerecord
      kaminari-core (= 1.2.2)
    kaminari-core (1.2.2)
    listen (3.7.1)
      rb-fsevent (~> 0.10, >= 0.10.3)
      rb-inotify (~> 0.9, >= 0.9.10)
    logger (1.7.0)
    loofah (2.21.3)
      crass (~> 1.0.2)
      nokogiri (>= 1.12.0)
    macaddr (1.7.2)
      systemu (~> 2.6.5)
    mail (2.8.1)
      mini_mime (>= 0.1.1)
      net-imap
      net-pop
      net-smtp
    marcel (1.0.2)
    matrix (0.4.2)
    method_source (1.0.0)
    mime-types (3.5.1)
      mime-types-data (~> 3.2015)
    mime-types-data (3.2023.0808)
    mini_magick (4.12.0)
    mini_mime (1.1.2)
    mini_portile2 (2.8.2)
    minitest (5.18.1)
    msgpack (1.7.1)
    multi_xml (0.6.0)
    nested_form (0.3.2)
    net-imap (0.3.6)
      date
      net-protocol
    net-pop (0.1.2)
      net-protocol
    net-protocol (0.2.1)
      timeout
    net-scp (4.0.0)
      net-ssh (>= 2.6.5, < 8.0.0)
    net-smtp (0.3.3)
      net-protocol
    net-ssh (7.1.0)
    netrc (0.11.0)
    nio4r (2.5.9)
    nokogiri (1.15.3)
      mini_portile2 (~> 2.8.2)
      racc (~> 1.4)
    nokogiri (1.15.3-x86_64-linux)
      racc (~> 1.4)
    ostruct (0.6.1)
    paper_trail (16.0.0)
      activerecord (>= 6.1)
      request_store (~> 1.4)
    paranoia (2.6.2)
      activerecord (>= 5.1, < 7.1)
    pg (1.4.6)
    public_suffix (5.0.3)
    puma (6.6.0)
      nio4r (~> 2.0)
    pundit (2.5.0)
      activesupport (>= 3.0.0)
    racc (1.7.1)
    rack (2.2.7)
    rack-cors (2.0.2)
      rack (>= 2.0.0)
    rack-test (2.1.0)
      rack (>= 1.3)
    rails (7.0.6)
      actioncable (= 7.0.6)
      actionmailbox (= 7.0.6)
      actionmailer (= 7.0.6)
      actionpack (= 7.0.6)
      actiontext (= 7.0.6)
      actionview (= 7.0.6)
      activejob (= 7.0.6)
      activemodel (= 7.0.6)
      activerecord (= 7.0.6)
      activestorage (= 7.0.6)
      activesupport (= 7.0.6)
      bundler (>= 1.15.0)
      railties (= 7.0.6)
    rails-dom-testing (2.1.1)
      activesupport (>= 5.0.0)
      minitest
      nokogiri (>= 1.6)
    rails-html-sanitizer (1.6.0)
      loofah (~> 2.21)
      nokogiri (~> 1.14)
    rails_best_practices (1.23.2)
      activesupport
      code_analyzer (~> 0.5.5)
      erubis
      i18n
      json
      require_all (~> 3.0)
      ruby-progressbar
    railties (7.0.6)
      actionpack (= 7.0.6)
      activesupport (= 7.0.6)
      method_source
      rake (>= 12.2)
      thor (~> 1.0)
      zeitwerk (~> 2.5)
    rake (13.0.6)
    ransack (4.0.0)
      activerecord (>= 6.1.5)
      activesupport (>= 6.1.5)
      i18n
    rb-fsevent (0.11.2)
    rb-inotify (0.10.1)
      ffi (~> 1.0)
    redis (4.8.1)
    regexp_parser (2.8.1)
    reline (0.3.5)
      io-console (~> 0.5)
    request_store (1.7.0)
      rack (>= 1.4)
    require_all (3.0.0)
    rest-client (2.1.0)
      http-accept (>= 1.7.0, < 2.0)
      http-cookie (>= 1.0.2, < 2.0)
      mime-types (>= 1.16, < 4.0)
      netrc (~> 0.8)
    rexml (3.2.5)
    roo (2.10.1)
      nokogiri (~> 1)
      rubyzip (>= 1.3.0, < 3.0.0)
    roo-xls (1.2.0)
      nokogiri
      roo (>= 2.0.0, < 3)
      spreadsheet (> 0.9.0)
    ruby-ole (********)
    ruby-progressbar (1.13.0)
    ruby-vips (2.1.4)
      ffi (~> 1.12)
    rubyzip (2.3.2)
    sass-rails (6.0.0)
      sassc-rails (~> 2.1, >= 2.1.1)
    sassc (2.4.0)
      ffi (~> 1.9)
    sassc-rails (2.1.2)
      railties (>= 4.0.0)
      sassc (>= 2.0)
      sprockets (> 3.0)
      sprockets-rails
      tilt
    selenium-webdriver (4.9.0)
      rexml (~> 3.2, >= 3.2.5)
      rubyzip (>= 1.2.2, < 3.0)
      websocket (~> 1.0)
    sexp_processor (4.17.0)
    simple_form (5.2.0)
      actionpack (>= 5.2)
      activemodel (>= 5.2)
    spreadsheet (1.3.4)
      bigdecimal
      logger
      ruby-ole
    spring (4.1.1)
    sprockets (4.2.0)
      concurrent-ruby (~> 1.0)
      rack (>= 2.2.4, < 4)
    sprockets-rails (3.4.2)
      actionpack (>= 5.2)
      activesupport (>= 5.2)
      sprockets (>= 3.0.0)
    sshkit (1.21.4)
      net-scp (>= 1.1.2)
      net-ssh (>= 2.8.0)
    ssrf_filter (1.1.1)
    stimulus-rails (1.2.1)
      railties (>= 6.0.0)
    systemu (2.6.5)
    thor (1.2.2)
    tilt (2.3.0)
    timeout (0.4.0)
    turbo-rails (1.4.0)
      actionpack (>= 6.0.0)
      activejob (>= 6.0.0)
      railties (>= 6.0.0)
    tzinfo (2.0.6)
      concurrent-ruby (~> 1.0)
    uuid (2.3.9)
      macaddr (~> 1.0)
    web-console (4.2.0)
      actionview (>= 6.0.0)
      activemodel (>= 6.0.0)
      bindex (>= 0.4.0)
      railties (>= 6.0.0)
    webdrivers (5.2.0)
      nokogiri (~> 1.6)
      rubyzip (>= 1.3.0)
      selenium-webdriver (~> 4.0)
    websocket (1.2.9)
    websocket-client-simple (0.6.1)
      event_emitter
      websocket
    websocket-driver (0.7.5)
      websocket-extensions (>= 0.1.0)
    websocket-extensions (0.1.5)
    whenever (1.0.0)
      chronic (>= 0.6.3)
    xpath (3.2.0)
      nokogiri (~> 1.8)
    zeitwerk (2.6.8)

PLATFORMS
  linux
  ruby

DEPENDENCIES
  aasm
  addressable
  admin_help
  aliyun-cloud_sms
  annotate
  bcrypt (~> 3.1.7)
  bootsnap
  capistrano
  capistrano-rails
  capistrano-rbenv
  capistrano3-puma (= 6.0.0.beta.1)
  capybara
  carrierwave (~> 3.0)
  caxlsx
  caxlsx_rails
  config
  debug
  deep_cloneable (~> 3.2.1)
  enum_help
  httparty (~> 0.18.0)
  importmap-rails
  jbuilder
  jquery-rails
  jwt
  kaminari
  listen (~> 3.3)
  mime-types
  nested_form
  paper_trail
  paranoia
  pg (>= 0.18, < 2.0)
  puma
  pundit
  rack-cors
  rails (~> 7.0.5)
  rails_best_practices
  ransack
  redis (~> 4.0)
  roo (~> 2.10.0)
  roo-xls
  sass-rails (>= 6)
  selenium-webdriver
  simple_form
  spring
  sprockets-rails
  stimulus-rails
  turbo-rails
  tzinfo-data
  web-console (>= 4.1.0)
  webdrivers
  websocket-client-simple
  whenever

BUNDLED WITH
   2.6.7
